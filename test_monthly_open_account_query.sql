-- 测试月度开户数据查询SQL
-- 这个SQL用于验证我们在EmployeeOpenAccountDataMonthlyController中实现的逻辑

-- 1. 查询某个员工在本月的开户数量（示例）
-- 假设员工昵称为'张三'，查询本月1号0点到今天0点的开户数
SELECT COUNT(1) as open_account_count
FROM enter_information 
WHERE audit_type = 0 
  AND account_specialist = '张三'
  AND update_time >= '2025-08-01 00:00:00'  -- 本月1号0点
  AND update_time < '2025-08-04 00:00:00';  -- 今天0点（不包含）

-- 2. 查询所有商务部门员工的开户数据（按部门分组）
-- 这个查询模拟了我们在代码中的逻辑
SELECT 
    su.dept_name,
    su.nick_name,
    COUNT(ei.id) as open_account_count
FROM (
    -- 模拟商务部门员工数据
    SELECT '商务一部' as dept_name, '张三' as nick_name
    UNION ALL SELECT '商务一部', '李四'
    UNION ALL SELECT '商务二部', '王五'
    UNION ALL SELECT '商务二部', '赵六'
    UNION ALL SELECT '商务三部', '孙七'
    UNION ALL SELECT '商务四部', '周八'
) su
LEFT JOIN enter_information ei ON ei.account_specialist = su.nick_name
    AND ei.audit_type = 0
    AND ei.update_time >= '2025-08-01 00:00:00'  -- 本月1号0点
    AND ei.update_time < '2025-08-04 00:00:00'   -- 今天0点（不包含）
GROUP BY su.dept_name, su.nick_name
ORDER BY su.dept_name, su.nick_name;

-- 3. 按部门汇总开户数据
SELECT 
    dept_name,
    SUM(open_account_count) as dept_total_count
FROM (
    SELECT 
        su.dept_name,
        su.nick_name,
        COUNT(ei.id) as open_account_count
    FROM (
        -- 模拟商务部门员工数据
        SELECT '商务一部' as dept_name, '张三' as nick_name
        UNION ALL SELECT '商务一部', '李四'
        UNION ALL SELECT '商务二部', '王五'
        UNION ALL SELECT '商务二部', '赵六'
        UNION ALL SELECT '商务三部', '孙七'
        UNION ALL SELECT '商务四部', '周八'
    ) su
    LEFT JOIN enter_information ei ON ei.account_specialist = su.nick_name
        AND ei.audit_type = 0
        AND ei.update_time >= '2025-08-01 00:00:00'  -- 本月1号0点
        AND ei.update_time < '2025-08-04 00:00:00'   -- 今天0点（不包含）
    GROUP BY su.dept_name, su.nick_name
) dept_data
GROUP BY dept_name
ORDER BY dept_name;

-- 4. 查询总开户数
SELECT 
    SUM(open_account_count) as total_open_account_count
FROM (
    SELECT 
        su.dept_name,
        su.nick_name,
        COUNT(ei.id) as open_account_count
    FROM (
        -- 模拟商务部门员工数据
        SELECT '商务一部' as dept_name, '张三' as nick_name
        UNION ALL SELECT '商务一部', '李四'
        UNION ALL SELECT '商务二部', '王五'
        UNION ALL SELECT '商务二部', '赵六'
        UNION ALL SELECT '商务三部', '孙七'
        UNION ALL SELECT '商务四部', '周八'
    ) su
    LEFT JOIN enter_information ei ON ei.account_specialist = su.nick_name
        AND ei.audit_type = 0
        AND ei.update_time >= '2025-08-01 00:00:00'  -- 本月1号0点
        AND ei.update_time < '2025-08-04 00:00:00'   -- 今天0点（不包含）
    GROUP BY su.dept_name, su.nick_name
) total_data;

-- 5. 验证enter_information表结构和数据
-- 查看表中的字段和一些示例数据
SELECT 
    id,
    audit_type,
    account_specialist,
    update_time,
    create_time
FROM enter_information 
WHERE audit_type = 0 
  AND account_specialist IS NOT NULL
  AND update_time >= '2025-08-01 00:00:00'
ORDER BY update_time DESC 
LIMIT 10;

-- 6. 检查不同audit_type的数据分布
SELECT 
    audit_type,
    COUNT(*) as count,
    CASE 
        WHEN audit_type = 0 THEN '审核通过'
        WHEN audit_type = 1 THEN '审核中'
        WHEN audit_type = 2 THEN '驳回'
        ELSE '未知状态'
    END as audit_type_desc
FROM enter_information 
GROUP BY audit_type
ORDER BY audit_type;
