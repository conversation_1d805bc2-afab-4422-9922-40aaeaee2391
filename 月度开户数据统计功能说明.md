# 月度开户数据统计功能说明

## 功能概述

本功能实现了查询本月（从本月1号0点到今天0点）每个部门中每个员工的开户数量统计，主要用于创建饼图数据展示。

## 实现文件

### 1. 控制器
- **文件**: `ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/EmployeeOpenAccountDataMonthlyController.java`
- **接口**: `POST /employee_backend_data/queryOpenAccountByDeptMonthly`
- **功能**: 查询每个部门每个人的开户数和每个人合计的数量，用于饼图展示

## 核心逻辑

### 1. 数据查询范围
- **时间范围**: 本月1号0点0分0秒 到 今天0点0分0秒（不包含今天）
- **部门范围**: 商务部下的所有子部门（商务一部、商务二部、商务三部、商务四部）
- **查询条件**: `audit_type = 0`（审核通过的记录）

### 2. 查询SQL
```sql
SELECT COUNT(1) 
FROM enter_information 
WHERE audit_type = 0 
  AND account_specialist = #{nickName} 
  AND update_time >= #{thisMonthBeginTime} 
  AND update_time < #{todayZero}
```

### 3. 数据处理流程
1. 查询商务部（deptId=104）下的所有用户
2. 过滤掉部门名称为"商务部"的用户
3. 按部门名称分组，每组包含该部门的所有员工昵称
4. 循环每个部门的每个员工，调用`countByTimeAndSpecialist`方法查询开户数
5. 累加每个部门的开户总数和全部门的开户总数
6. 构建适合饼图使用的数据结构

## 返回数据结构

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "deptDataList": [
      {
        "deptName": "商务一部",
        "employees": [
          {
            "nickName": "张三",
            "openAccountCount": 15
          },
          {
            "nickName": "李四", 
            "openAccountCount": 12
          }
        ],
        "deptTotalCount": 27
      },
      {
        "deptName": "商务二部",
        "employees": [
          {
            "nickName": "王五",
            "openAccountCount": 18
          }
        ],
        "deptTotalCount": 18
      }
    ],
    "totalOpenAccountCount": 45,
    "startTime": "2025-08-01 00:00:00",
    "endTime": "2025-08-04 00:00:00"
  }
}
```

## 数据字段说明

### deptDataList（部门数据列表）
- `deptName`: 部门名称
- `employees`: 该部门员工开户数据列表
- `deptTotalCount`: 该部门开户总数

### employees（员工数据列表）
- `nickName`: 员工昵称
- `openAccountCount`: 该员工的开户数量

### 其他字段
- `totalOpenAccountCount`: 所有部门的开户总数
- `startTime`: 查询开始时间
- `endTime`: 查询结束时间

## 饼图数据使用

### 1. 部门维度饼图
可以使用 `deptDataList` 中每个部门的 `deptTotalCount` 创建部门维度的饼图：
```javascript
const deptPieData = data.deptDataList.map(dept => ({
  name: dept.deptName,
  value: dept.deptTotalCount
}));
```

### 2. 员工维度饼图
可以将所有员工的开户数据合并创建员工维度的饼图：
```javascript
const employeePieData = [];
data.deptDataList.forEach(dept => {
  dept.employees.forEach(emp => {
    employeePieData.push({
      name: `${dept.deptName}-${emp.nickName}`,
      value: emp.openAccountCount
    });
  });
});
```

## 依赖的服务方法

### EnterInformationService.countByTimeAndSpecialist()
- **参数**: 
  - `startTime`: 开始时间
  - `endTime`: 结束时间  
  - `accountSpecialist`: 客户专员昵称
  - `auditType`: 审核类型（0=审核通过）
- **返回**: 开户数量（int）

## 测试验证

### 1. 接口测试
```bash
curl -X POST http://localhost:8080/employee_backend_data/queryOpenAccountByDeptMonthly
```

### 2. SQL验证
可以使用 `test_monthly_open_account_query.sql` 文件中的SQL语句验证数据的正确性。

## 注意事项

1. **时间范围**: 查询的是本月1号到今天0点的数据，不包含今天的数据
2. **部门过滤**: 会自动过滤掉部门名称为"商务部"的用户，只统计子部门
3. **审核状态**: 只统计 `audit_type = 0`（审核通过）的记录
4. **空值处理**: 如果某个员工没有开户记录，其开户数量为0
5. **性能考虑**: 如果员工数量较多，建议考虑添加缓存机制

## 扩展功能

如需要其他时间范围的统计（如昨天、7天、本年），可以参考现有的时间处理逻辑进行扩展。
