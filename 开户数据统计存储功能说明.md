# 开户数据统计存储功能说明

## 功能概述
在原有开户数据统计功能基础上，新增了将统计结果存储到数据库的功能。系统会按照stat_type维度（daily、weekly、monthly、yearly）将统计数据保存到两个专门的统计表中。

## 数据库表结构

### 1. 部门开户数据统计表 (dept_open_account_statistics)
```sql
CREATE TABLE `dept_open_account_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `dept_name` varchar(100) NOT NULL COMMENT '部门名称',
  `stat_type` varchar(20) NOT NULL COMMENT '统计类型：daily-昨天,weekly-7天,monthly-本月,yearly-本年',
  `open_count` int(11) NOT NULL DEFAULT '0' COMMENT '开户数量',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `start_time` datetime NOT NULL COMMENT '统计开始时间',
  `end_time` datetime NOT NULL COMMENT '统计结束时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_dept_stat_date` (`dept_name`, `stat_type`, `stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门开户数据统计表';
```

### 2. 总开户数据统计表 (total_open_account_statistics)
```sql
CREATE TABLE `total_open_account_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `stat_type` varchar(20) NOT NULL COMMENT '统计类型：daily-昨天,weekly-7天,monthly-本月,yearly-本年',
  `total_count` int(11) NOT NULL DEFAULT '0' COMMENT '总开户数量',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `start_time` datetime NOT NULL COMMENT '统计开始时间',
  `end_time` datetime NOT NULL COMMENT '统计结束时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_stat_type_date` (`stat_type`, `stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='总开户数据统计表';
```

## 新增文件列表

### 1. 数据库建表脚本
- `dept_open_account_statistics.sql` - 建表语句

### 2. Domain实体类
- `ruoyi-system/src/main/java/com/ruoyi/system/domain/DeptOpenAccountStatistics.java`
- `ruoyi-system/src/main/java/com/ruoyi/system/domain/TotalOpenAccountStatistics.java`

### 3. Mapper接口
- `ruoyi-system/src/main/java/com/ruoyi/system/mapper/DeptOpenAccountStatisticsMapper.java`
- `ruoyi-system/src/main/java/com/ruoyi/system/mapper/TotalOpenAccountStatisticsMapper.java`

### 4. Mapper XML文件
- `ruoyi-system/src/main/resources/mapper/system/DeptOpenAccountStatisticsMapper.xml`
- `ruoyi-system/src/main/resources/mapper/system/TotalOpenAccountStatisticsMapper.xml`

### 5. Service接口
- `ruoyi-system/src/main/java/com/ruoyi/system/service/IDeptOpenAccountStatisticsService.java`
- `ruoyi-system/src/main/java/com/ruoyi/system/service/ITotalOpenAccountStatisticsService.java`

### 6. Service实现类
- `ruoyi-system/src/main/java/com/ruoyi/system/service/impl/DeptOpenAccountStatisticsServiceImpl.java`
- `ruoyi-system/src/main/java/com/ruoyi/system/service/impl/TotalOpenAccountStatisticsServiceImpl.java`

## 功能特性

### 1. 数据存储逻辑
- 每次调用开户数据统计接口时，会自动将统计结果保存到数据库
- 使用 `INSERT ... ON DUPLICATE KEY UPDATE` 语句，支持数据的插入和更新
- 按照当前日期作为统计日期进行存储

### 2. 统计维度
- **daily**: 昨天的开户数据
- **weekly**: 最近7天的开户数据
- **monthly**: 本月的开户数据
- **yearly**: 本年的开户数据

### 3. 存储内容
- **总开户数统计**: 每个时间维度的总开户数量
- **部门开户数统计**: 每个部门在每个时间维度的开户数量（包括"其他"）

### 4. 数据完整性
- 使用唯一索引防止重复数据
- 支持数据更新，每次统计会刷新当天的数据
- 异常处理：数据库保存失败不会影响接口正常返回

## 使用方式

### 1. 执行建表脚本
```bash
# 在数据库中执行建表脚本
mysql -u username -p database_name < dept_open_account_statistics.sql
```

### 2. 调用接口
```bash
# 调用开户数据统计接口，会自动保存数据到数据库
curl -X POST http://localhost:8080/employee_backend_data/queryOpenAccountAmount
```

### 3. 查询历史数据
可以通过Service层方法查询历史统计数据：
```java
// 查询某部门某天的统计数据
DeptOpenAccountStatistics stats = deptOpenAccountStatisticsService
    .selectByDeptNameAndStatTypeAndDate("商务一部", "daily", statDate);

// 查询某天的总开户数统计
TotalOpenAccountStatistics totalStats = totalOpenAccountStatisticsService
    .selectByStatTypeAndDate("daily", statDate);
```

## 数据示例

### 部门开户数据统计表数据示例
| id | dept_name | stat_type | open_count | stat_date | start_time | end_time |
|----|-----------|-----------|------------|-----------|------------|----------|
| 1  | 商务一部  | daily     | 15         | 2024-08-04| 2024-08-03 00:00:00 | 2024-08-04 00:00:00 |
| 2  | 商务一部  | weekly    | 85         | 2024-08-04| 2024-07-28 00:00:00 | 2024-08-04 00:00:00 |
| 3  | 其他      | daily     | 5          | 2024-08-04| 2024-08-03 00:00:00 | 2024-08-04 00:00:00 |

### 总开户数据统计表数据示例
| id | stat_type | total_count | stat_date | start_time | end_time |
|----|-----------|-------------|-----------|------------|----------|
| 1  | daily     | 120         | 2024-08-04| 2024-08-03 00:00:00 | 2024-08-04 00:00:00 |
| 2  | weekly    | 650         | 2024-08-04| 2024-07-28 00:00:00 | 2024-08-04 00:00:00 |

## 注意事项

1. **数据库权限**: 确保应用有对应表的读写权限
2. **索引优化**: 已创建必要的索引，支持高效查询
3. **数据一致性**: 使用唯一索引保证数据不重复
4. **异常处理**: 数据库操作异常不会影响接口正常响应
5. **性能考虑**: 批量操作使用事务，提高性能
