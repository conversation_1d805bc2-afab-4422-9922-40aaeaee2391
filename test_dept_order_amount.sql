-- 测试部门订单交易额统计功能的SQL语句

-- 1. 创建测试表（如果还没有创建的话）
-- CREATE TABLE `dept_order_amount_statistics` (
--   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
--   `stat_type` enum('daily','weekly','monthly','yearly') NOT NULL COMMENT '统计类型：daily-昨天,weekly-7天,monthly-本月,yearly-本年',
--   `dept_name` varchar(100) NOT NULL COMMENT '部门名称，其他交易额用"其他"表示',
--   `total_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '部门交易总额',
--   `stat_date` date NOT NULL COMMENT '统计日期',
--   `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
--   `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
--   PRIMARY KEY (`id`),
--   UNIQUE KEY `unique_stat` (`stat_type`,`dept_name`,`stat_date`),
--   KEY `idx_stat_type` (`stat_type`),
--   KEY `idx_dept_name` (`dept_name`),
--   KEY `idx_stat_date` (`stat_date`)
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门订单交易额统计表';

-- 2. 查询统计数据
SELECT 
    stat_type,
    dept_name,
    total_amount,
    stat_date,
    create_time
FROM dept_order_amount_statistics 
ORDER BY stat_date DESC, stat_type, dept_name;

-- 3. 按统计类型查询
SELECT 
    stat_type,
    dept_name,
    total_amount,
    stat_date
FROM dept_order_amount_statistics 
WHERE stat_type = 'daily'
ORDER BY stat_date DESC, dept_name;

-- 4. 按部门查询
SELECT 
    stat_type,
    dept_name,
    total_amount,
    stat_date
FROM dept_order_amount_statistics 
WHERE dept_name = '商务一部'
ORDER BY stat_date DESC, stat_type;

-- 5. 查询最新的统计数据
SELECT 
    stat_type,
    dept_name,
    total_amount,
    stat_date
FROM dept_order_amount_statistics 
WHERE stat_date = (SELECT MAX(stat_date) FROM dept_order_amount_statistics)
ORDER BY stat_type, dept_name;

-- 6. 清理测试数据（如果需要）
-- DELETE FROM dept_order_amount_statistics WHERE stat_date = CURDATE();
