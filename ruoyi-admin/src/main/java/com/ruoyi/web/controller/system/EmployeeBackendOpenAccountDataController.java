package com.ruoyi.web.controller.system;

import com.alibaba.druid.util.StringUtils;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.DeptOpenAccountStatistics;
import com.ruoyi.system.domain.TotalOpenAccountStatistics;
import com.ruoyi.system.service.IDeptOpenAccountStatisticsService;
import com.ruoyi.system.service.IEnterInformationService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.ITotalOpenAccountStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/employee_backend_data")
public class EmployeeBackendOpenAccountDataController {
    @Autowired
    private IEnterInformationService enterInformationService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private IDeptOpenAccountStatisticsService deptOpenAccountStatisticsService;
    @Autowired
    private ITotalOpenAccountStatisticsService totalOpenAccountStatisticsService;
    @PostMapping(value = "/queryOpenAccountAmount")
    public AjaxResult queryOpenAccountAmount() {
        Long deptId = 104L;
        List<SysUser> sysUsers = userService.selectUserListByDeptId(deptId);
        //去除dept对象中deptName为商务部的数据
        sysUsers.removeIf(sysUser -> sysUser.getDept()!=null && StringUtils.equals("商务部", sysUser.getDept().getDeptName()));
        //然后根据SysUser中的dept对象中deptName将用户分组,分组list中只需保存nickName就可以. 用户将会被分为商务一部,商务二部,商务三部和商务四部
        Map<String, List<String>> deptUserNickNamesMap = sysUsers.stream().collect(Collectors.groupingBy(sysUser -> sysUser.getDept().getDeptName(), Collectors.mapping(SysUser::getNickName, Collectors.toList())));
        //创建统计时间范围,昨天,7天,本月,本年
        Calendar calendar = getZeroTime();
        Date todayZero = calendar.getTime();
        //创建昨天0点0分0秒
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        Date yesterdayZero = calendar.getTime();
        calendar = getZeroTime();
        //创建距离今日0点7天的时间
        calendar.add(Calendar.DAY_OF_MONTH, -7);
        Date sevenDaysAgo = calendar.getTime();
        //创建这个月1号0点0分0秒
        calendar = getZeroTime();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date thisMonthBeginTime = calendar.getTime();
        //创建本年1月1号0点0分0秒
        calendar = getZeroTime();
        calendar.set(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date thisYearBeginTime = calendar.getTime();
        //创建统计时间范围,昨天,7天,本月,本年的开始结束时间对;statType为'daily','weekly','monthly','yearly'
        Map<String, Date[]> timeRangeMap = new HashMap<>();
        timeRangeMap.put("daily", new Date[]{yesterdayZero, todayZero});
        timeRangeMap.put("weekly", new Date[]{sevenDaysAgo, todayZero});
        timeRangeMap.put("monthly", new Date[]{thisMonthBeginTime, todayZero});
        timeRangeMap.put("yearly", new Date[]{thisYearBeginTime, todayZero});
        // 存储最终结果
        Map<String, Map<String, Object>> resultMap = new HashMap<>();

        // 先按timeRangeMap循环，计算每个时间范围的总开户数
        Map<String, Integer> totalOpenAccountMap = new HashMap<>();
        for (Map.Entry<String, Date[]> entry1 : timeRangeMap.entrySet()) {
            String statType = entry1.getKey();
            Date[] timeRange = entry1.getValue();
            Date startTime = timeRange[0];
            Date endTime = timeRange[1];

            // 查询该时间范围的总开户数（audit_type=0）
            int totalCount = enterInformationService.countByTimeAndAuditType(startTime, endTime, 0);
            totalOpenAccountMap.put(statType, totalCount);
        }

        // 再按部门deptUserNickNamesMap循环,再按timeRangeMap循环
        for (Map.Entry<String, List<String>> entry : deptUserNickNamesMap.entrySet()) {
            String deptName = entry.getKey();
            List<String> nickNameList = entry.getValue();

            Map<String, Object> deptData = new HashMap<>();

            // 按timeRangeMap循环，计算每个时间范围该部门的开户数
            for (Map.Entry<String, Date[]> entry1 : timeRangeMap.entrySet()) {
                String statType = entry1.getKey();
                Date[] timeRange = entry1.getValue();
                Date startTime = timeRange[0];
                Date endTime = timeRange[1];

                // 计算该部门在该时间范围的开户数
                int deptOpenCount = 0;
                for (String nickName : nickNameList) {
                    // 查询每个员工的开户数量
                    int count = enterInformationService.countByTimeAndSpecialist(startTime, endTime, nickName, 0);
                    deptOpenCount += count;
                }

                deptData.put(statType, deptOpenCount);
            }

            resultMap.put(deptName, deptData);
        }

        // 计算"其他"开户数：总开户数 - 全部部门开户数之和
        Map<String, Object> otherData = new HashMap<>();
        for (Map.Entry<String, Date[]> entry1 : timeRangeMap.entrySet()) {
            String statType = entry1.getKey();
            int totalCount = totalOpenAccountMap.get(statType);

            // 计算所有部门的开户数之和
            int allDeptCount = 0;
            for (Map.Entry<String, Map<String, Object>> deptEntry : resultMap.entrySet()) {
                Map<String, Object> deptData = deptEntry.getValue();
                allDeptCount += (Integer) deptData.get(statType);
            }

            // 其他开户数 = 总开户数 - 全部部门开户数之和
            int otherCount = totalCount - allDeptCount;
            otherData.put(statType, otherCount);
        }

        // 将"其他"数据添加到结果中
        resultMap.put("其他", otherData);

        // 保存统计数据到数据库
        saveStatisticsToDatabase(timeRangeMap, totalOpenAccountMap, resultMap);

        // 同时返回总开户数信息
        Map<String, Object> finalResult = new HashMap<>();
        finalResult.put("totalOpenAccount", totalOpenAccountMap);
        finalResult.put("deptOpenAccount", resultMap);

        return AjaxResult.success(finalResult);
    }


    /**
     * 保存统计数据到数据库
     *
     * @param timeRangeMap 时间范围映射
     * @param totalOpenAccountMap 总开户数映射
     * @param resultMap 部门开户数映射
     */
    private void saveStatisticsToDatabase(Map<String, Date[]> timeRangeMap,
                                        Map<String, Integer> totalOpenAccountMap,
                                        Map<String, Map<String, Object>> resultMap) {
        try {
            Date currentDate = new Date();
            Date statDate = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-MM-dd", currentDate));

            // 保存总开户数统计
            for (Map.Entry<String, Date[]> entry : timeRangeMap.entrySet()) {
                String statType = entry.getKey();
                Date[] timeRange = entry.getValue();
                Date startTime = timeRange[0];
                Date endTime = timeRange[1];
                Integer totalCount = totalOpenAccountMap.get(statType);

                TotalOpenAccountStatistics totalStats = new TotalOpenAccountStatistics(
                    statType, totalCount, statDate, startTime, endTime);
                totalOpenAccountStatisticsService.insertOrUpdateTotalOpenAccountStatistics(totalStats);
            }

            // 保存部门开户数统计
            for (Map.Entry<String, Map<String, Object>> deptEntry : resultMap.entrySet()) {
                String deptName = deptEntry.getKey();
                Map<String, Object> deptData = deptEntry.getValue();

                for (Map.Entry<String, Date[]> entry : timeRangeMap.entrySet()) {
                    String statType = entry.getKey();
                    Date[] timeRange = entry.getValue();
                    Date startTime = timeRange[0];
                    Date endTime = timeRange[1];
                    Integer openCount = (Integer) deptData.get(statType);

                    DeptOpenAccountStatistics deptStats = new DeptOpenAccountStatistics(
                        deptName, statType, openCount, statDate, startTime, endTime);
                    deptOpenAccountStatisticsService.insertOrUpdateDeptOpenAccountStatistics(deptStats);
                }
            }
        } catch (Exception e) {
            // 记录日志，但不影响主流程
            System.err.println("保存统计数据到数据库失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static Calendar getZeroTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar;
    }
}
