package com.ruoyi.web.controller.system;

import com.alibaba.druid.util.StringUtils;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.DeptOpenAccountStatistics;
import com.ruoyi.system.domain.TotalOpenAccountStatistics;
import com.ruoyi.system.service.IDeptOpenAccountStatisticsService;
import com.ruoyi.system.service.IEnterInformationService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.ITotalOpenAccountStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/employee_backend_data")
public class EmployeeOpenAccountDataMonthlyController {
    @Autowired
    private IEnterInformationService enterInformationService;
    @Autowired
    private ISysUserService userService;

    /**
     * 每个部门每个人的开户数和每个人合计的数量,是饼图使用
     * @return
     */
    @PostMapping(value = "/queryOpenAccountByDeptMonthly")
    public AjaxResult queryOpenAccountByDeptMonthly() {
        Long deptId = 104L;
        List<SysUser> sysUsers = userService.selectUserListByDeptId(deptId);
        //去除dept对象中deptName为商务部的数据
        sysUsers.removeIf(sysUser -> sysUser.getDept()!=null && StringUtils.equals("商务部", sysUser.getDept().getDeptName()));
        //然后根据SysUser中的dept对象中deptName将用户分组,分组list中只需保存nickName就可以. 用户将会被分为商务一部,商务二部,商务三部和商务四部
        Map<String, List<String>> deptUserNickNamesMap = sysUsers.stream().collect(Collectors.groupingBy(sysUser -> sysUser.getDept().getDeptName(), Collectors.mapping(SysUser::getNickName, Collectors.toList())));
        //创建统计时间范围,本月
        Calendar calendar = getZeroTime();
        Date todayZero = calendar.getTime();
        //创建这个月1号0点0分0秒
        calendar = getZeroTime();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date thisMonthBeginTime = calendar.getTime();

        //查询thisMonthBeginTime到todayZero时间范围的开户数量
        //循环deptUserNickNamesMap,查询每个人的开户数,并累加每个人的开户数获取总数,这个是创建饼图使用
        List<Map<String, Object>> deptDataList = new ArrayList<>();
        int totalOpenAccountCount = 0;

        for (Map.Entry<String, List<String>> entry : deptUserNickNamesMap.entrySet()) {
            String deptName = entry.getKey();
            List<String> nickNameList = entry.getValue();

            // 创建部门数据
            Map<String, Object> deptData = new HashMap<>();
            deptData.put("deptName", deptName);

            // 创建员工开户数据列表
            List<Map<String, Object>> employeeDataList = new ArrayList<>();
            int deptTotalCount = 0;

            // 循环查询每个员工的开户数
            for (String nickName : nickNameList) {
                // 查询某个员工的开户数量
                // SQL: select count(1) from enter_information where audit_type=0 and account_specialist=#{nickName} and update_time >= #{startTime} AND update_time < #{endTime}
                int employeeOpenAccountCount = enterInformationService.countByTimeAndSpecialist(
                    thisMonthBeginTime,
                    todayZero,
                    nickName,
                    0  // audit_type=0 表示审核通过
                );

                // 创建员工数据
                Map<String, Object> employeeData = new HashMap<>();
                employeeData.put("nickName", nickName);
                employeeData.put("openAccountCount", employeeOpenAccountCount);
                employeeDataList.add(employeeData);

                // 累加部门总数
                deptTotalCount += employeeOpenAccountCount;
            }

            deptData.put("employees", employeeDataList);
            deptData.put("deptTotalCount", deptTotalCount);
            deptDataList.add(deptData);

            // 累加总开户数
            totalOpenAccountCount += deptTotalCount;
        }

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("deptDataList", deptDataList);
        result.put("totalOpenAccountCount", totalOpenAccountCount);
        result.put("startTime", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", thisMonthBeginTime));
        result.put("endTime", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", todayZero));

        return AjaxResult.success(result);
    }

    private static Calendar getZeroTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar;
    }
}
