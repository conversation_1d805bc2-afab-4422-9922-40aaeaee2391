package com.ruoyi.web.controller.system;

import com.alibaba.druid.util.StringUtils;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.service.IEmployeeSalesStatisticsService;
import com.ruoyi.system.service.IEnterInformationService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/employee_backend_data")
public class EmployeeDataRankingListController {
    @Autowired
    private IEnterInformationService enterInformationService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private IEmployeeSalesStatisticsService employeeSalesStatisticsService;
    /**
     * 所有员工销售额排行,取前6个,时间范围用参数来选定
     * @return
     */
    @GetMapping(value = "/queryOrderAmountRankList")
    public AjaxResult queryOrderAmountRankList(@RequestParam("statType") String statType) {
        Long deptId = 104L;
        List<SysUser> sysUsers = userService.selectUserListByDeptId(deptId);
        //去除dept对象中deptName为商务部的数据
        sysUsers.removeIf(sysUser -> sysUser.getDept()!=null && StringUtils.equals("商务部", sysUser.getDept().getDeptName()));
        //从sysUsers中获取所有userId
        List<Long> userIds = sysUsers.stream().map(SysUser::getUserId).collect(Collectors.toList());
        //创建统计时间范围,昨天,7天,本月,本年
        Calendar calendar = getZeroTime();
        Date todayZero = calendar.getTime();
        //创建昨天0点0分0秒
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        Date yesterdayZero = calendar.getTime();
        calendar = getZeroTime();
        //创建距离今日0点7天的时间
        calendar.add(Calendar.DAY_OF_MONTH, -7);
        Date sevenDaysAgo = calendar.getTime();
        //创建这个月1号0点0分0秒
        calendar = getZeroTime();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date thisMonthBeginTime = calendar.getTime();
        //创建本年1月1号0点0分0秒
        calendar = getZeroTime();
        calendar.set(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date thisYearBeginTime = calendar.getTime();
        //创建统计时间范围,昨天,7天,本月,本年的开始结束时间对;statType为'daily','weekly','monthly','yearly'
        Map<String, Date[]> timeRangeMap = new HashMap<>();
        timeRangeMap.put("daily", new Date[]{yesterdayZero, todayZero});
        timeRangeMap.put("weekly", new Date[]{sevenDaysAgo, todayZero});
        timeRangeMap.put("monthly", new Date[]{thisMonthBeginTime, todayZero});
        timeRangeMap.put("yearly", new Date[]{thisYearBeginTime, todayZero});
        //获取参数匹配的时间开始和时间结束
        Date[] timeRange = timeRangeMap.get(statType);
        //如果不存在则返回错误
        if (timeRange == null) {
            return AjaxResult.error("参数错误");
        }
        //使用employeeSalesStatisticsService按statType和userIds查询,并同时满足条件dept_id is null,并按total_sales字段从大到小排序,取前6个,输出userId和个人总销售额.
        //然后再把userId转换成nickName
        return AjaxResult.success();
    }

    /**
     * 所有员工开户数排行,取前6个,时间范围为参数传入
     * @return
     */
    @GetMapping(value = "/queryOpenAccountRankList")
    public AjaxResult queryOpenAccountRankList(@RequestParam("statType") String statType) {
        Long deptId = 104L;
        List<SysUser> sysUsers = userService.selectUserListByDeptId(deptId);
        //去除dept对象中deptName为商务部的数据
        sysUsers.removeIf(sysUser -> sysUser.getDept()!=null && StringUtils.equals("商务部", sysUser.getDept().getDeptName()));
        //从sysUsers中获取所有nickName
        List<String> nickNames = sysUsers.stream().map(SysUser::getNickName).collect(Collectors.toList());
        //创建统计时间范围,昨天,7天,本月,本年
        Calendar calendar = getZeroTime();
        Date todayZero = calendar.getTime();
        //创建昨天0点0分0秒
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        Date yesterdayZero = calendar.getTime();
        calendar = getZeroTime();
        //创建距离今日0点7天的时间
        calendar.add(Calendar.DAY_OF_MONTH, -7);
        Date sevenDaysAgo = calendar.getTime();
        //创建这个月1号0点0分0秒
        calendar = getZeroTime();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date thisMonthBeginTime = calendar.getTime();
        //创建本年1月1号0点0分0秒
        calendar = getZeroTime();
        calendar.set(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date thisYearBeginTime = calendar.getTime();
        //创建统计时间范围,昨天,7天,本月,本年的开始结束时间对;statType为'daily','weekly','monthly','yearly'
        Map<String, Date[]> timeRangeMap = new HashMap<>();
        timeRangeMap.put("daily", new Date[]{yesterdayZero, todayZero});
        timeRangeMap.put("weekly", new Date[]{sevenDaysAgo, todayZero});
        timeRangeMap.put("monthly", new Date[]{thisMonthBeginTime, todayZero});
        timeRangeMap.put("yearly", new Date[]{thisYearBeginTime, todayZero});

        //获取参数匹配的时间开始和时间结束
        Date[] timeRange = timeRangeMap.get(statType);
        //如果不存在则返回错误
        if (timeRange == null) {
            return AjaxResult.error("参数错误");
        }
        //按timeRange和nickNames获取所有员工的开户排行,取前6个,输出nickName 和开户数
        Date startTime = timeRange[0];
        Date endTime = timeRange[1];

        // 调用服务方法查询开户排行，取前6个，audit_type=0表示审核通过
        List<Map<String, Object>> rankingList = enterInformationService.queryOpenAccountRankingByNickNames(
            startTime,
            endTime,
            nickNames,
            0,  // audit_type=0 表示审核通过
            6   // 限制返回前6个
        );

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("rankingList", rankingList);
        result.put("statType", statType);
        result.put("startTime", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", startTime));
        result.put("endTime", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", endTime));
        result.put("totalCount", rankingList.size());

        return AjaxResult.success(result);
    }

    private static Calendar getZeroTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar;
    }
}
