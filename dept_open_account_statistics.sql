-- 部门开户数据统计表
CREATE TABLE `dept_open_account_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `dept_name` varchar(100) NOT NULL COMMENT '部门名称',
  `stat_type` varchar(20) NOT NULL COMMENT '统计类型：daily-昨天,weekly-7天,monthly-本月,yearly-本年',
  `open_count` int(11) NOT NULL DEFAULT '0' COMMENT '开户数量',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `start_time` datetime NOT NULL COMMENT '统计开始时间',
  `end_time` datetime NOT NULL COMMENT '统计结束时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_dept_stat_date` (`dept_name`, `stat_type`, `stat_date`) COMMENT '部门+统计类型+统计日期唯一索引',
  KEY `idx_stat_date` (`stat_date`) COMMENT '统计日期索引',
  KEY `idx_dept_name` (`dept_name`) COMMENT '部门名称索引',
  KEY `idx_stat_type` (`stat_type`) COMMENT '统计类型索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门开户数据统计表';

-- 总开户数据统计表
CREATE TABLE `total_open_account_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `stat_type` varchar(20) NOT NULL COMMENT '统计类型：daily-昨天,weekly-7天,monthly-本月,yearly-本年',
  `total_count` int(11) NOT NULL DEFAULT '0' COMMENT '总开户数量',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `start_time` datetime NOT NULL COMMENT '统计开始时间',
  `end_time` datetime NOT NULL COMMENT '统计结束时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_stat_type_date` (`stat_type`, `stat_date`) COMMENT '统计类型+统计日期唯一索引',
  KEY `idx_stat_date` (`stat_date`) COMMENT '统计日期索引',
  KEY `idx_stat_type` (`stat_type`) COMMENT '统计类型索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='总开户数据统计表';
