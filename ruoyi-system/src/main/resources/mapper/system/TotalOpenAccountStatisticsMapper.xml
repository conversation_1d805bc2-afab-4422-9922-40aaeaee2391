<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TotalOpenAccountStatisticsMapper">
    
    <resultMap type="TotalOpenAccountStatistics" id="TotalOpenAccountStatisticsResult">
        <result property="id"    column="id"    />
        <result property="statType"    column="stat_type"    />
        <result property="totalCount"    column="total_count"    />
        <result property="statDate"    column="stat_date"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTotalOpenAccountStatisticsVo">
        select id, stat_type, total_count, stat_date, start_time, end_time, create_time, update_time from total_open_account_statistics
    </sql>

    <select id="selectTotalOpenAccountStatisticsList" parameterType="TotalOpenAccountStatistics" resultMap="TotalOpenAccountStatisticsResult">
        <include refid="selectTotalOpenAccountStatisticsVo"/>
        <where>  
            <if test="statType != null  and statType != ''"> and stat_type = #{statType}</if>
            <if test="totalCount != null "> and total_count = #{totalCount}</if>
            <if test="statDate != null "> and stat_date = #{statDate}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
        </where>
        order by stat_date desc, create_time desc
    </select>
    
    <select id="selectTotalOpenAccountStatisticsById" parameterType="Long" resultMap="TotalOpenAccountStatisticsResult">
        <include refid="selectTotalOpenAccountStatisticsVo"/>
        where id = #{id}
    </select>

    <select id="selectByStatTypeAndDate" resultMap="TotalOpenAccountStatisticsResult">
        <include refid="selectTotalOpenAccountStatisticsVo"/>
        where stat_type = #{statType} and stat_date = #{statDate}
    </select>
        
    <insert id="insertTotalOpenAccountStatistics" parameterType="TotalOpenAccountStatistics" useGeneratedKeys="true" keyProperty="id">
        insert into total_open_account_statistics
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="statType != null and statType != ''">stat_type,</if>
            <if test="totalCount != null">total_count,</if>
            <if test="statDate != null">stat_date,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="statType != null and statType != ''">#{statType},</if>
            <if test="totalCount != null">#{totalCount},</if>
            <if test="statDate != null">#{statDate},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTotalOpenAccountStatistics" parameterType="TotalOpenAccountStatistics">
        update total_open_account_statistics
        <trim prefix="SET" suffixOverrides=",">
            <if test="statType != null and statType != ''">stat_type = #{statType},</if>
            <if test="totalCount != null">total_count = #{totalCount},</if>
            <if test="statDate != null">stat_date = #{statDate},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTotalOpenAccountStatisticsById" parameterType="Long">
        delete from total_open_account_statistics where id = #{id}
    </delete>

    <delete id="deleteTotalOpenAccountStatisticsByIds" parameterType="String">
        delete from total_open_account_statistics where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 批量插入 -->
    <insert id="batchInsertTotalOpenAccountStatistics" parameterType="java.util.List">
        insert into total_open_account_statistics (stat_type, total_count, stat_date, start_time, end_time, create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.statType}, #{item.totalCount}, #{item.statDate}, #{item.startTime}, #{item.endTime}, now(), now())
        </foreach>
    </insert>

    <!-- 插入或更新 -->
    <insert id="insertOrUpdateTotalOpenAccountStatistics" parameterType="TotalOpenAccountStatistics">
        insert into total_open_account_statistics (stat_type, total_count, stat_date, start_time, end_time, create_time, update_time)
        values (#{statType}, #{totalCount}, #{statDate}, #{startTime}, #{endTime}, now(), now())
        on duplicate key update
        total_count = #{totalCount},
        start_time = #{startTime},
        end_time = #{endTime},
        update_time = now()
    </insert>

</mapper>
