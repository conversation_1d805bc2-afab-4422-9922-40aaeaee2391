<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.EnterInformationMapper">
    
    <resultMap type="EnterInformation" id="EnterInformationResult">
        <result property="id"    column="id"    />
        <result property="entityType"    column="entity_type"    />
        <result property="frontPath"    column="front_path"    />
        <result property="backPath"    column="back_path"    />
        <result property="realName"    column="real_name"    />
        <result property="idNumber"    column="id_number"    />
        <result property="teacherName"    column="teacher_name"    />
        <result property="businessLicenseCompanyName"    column="business_license_company_name"    />
        <result property="businessLicenseNo"    column="business_license_no"    />
        <result property="businessLicensePath"    column="business_license_path"    />
        <result property="telNum"    column="tel_num"    />
        <result property="courseForm"    column="course_form"    />
        <result property="firstClassId"    column="first_class_id"    />
        <result property="firstClassPid"    column="first_class_pid"    />
        <result property="firstClassTitle"    column="first_class_title"    />
        <result property="firstClassDouyinClassId"    column="first_class_douyin_class_id"    />
        <result property="secondClassId"    column="second_class_id"    />
        <result property="secondClassPid"    column="second_class_pid"    />
        <result property="secondClassTitle"    column="second_class_title"    />
        <result property="secondClassDouyinClassId"    column="second_class_douyin_class_id"    />
        <result property="shopNickname"    column="shop_nickname"    />
        <result property="shopAvatarUrl"    column="shop_avatar_url"    />
        <result property="shopDesc"    column="shop_desc"    />
        <result property="platform"    column="platform"    />
        <result property="dyAccount"    column="dy_account"    />
        <result property="dyUid"    column="dy_uid"    />
        <result property="ksAccount"    column="ks_account"    />
        <result property="wxAccount"    column="wx_account"    />
        <result property="sphAccount"    column="sph_account"    />
        <result property="serviceBeginTime"    column="service_begin_time"    />
        <result property="serviceEndTime"    column="service_end_time"    />
        <result property="version"    column="version"    />
        <result property="rateType"    column="rate_type"    />
        <result property="rate"    column="rate"    />
        <result property="accountSpecialist"    column="account_specialist"    />
        <result property="customerServiceSpecialist"    column="customer_service_specialist"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="shopAccount"    column="shop_account"    />
        <result property="shopId"    column="shop_id"    />
        <result property="auditType"    column="audit_type"    />
        <result property="rejectReason"    column="reject_reason"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="dyFansNum"    column="dy_fans_num"    />
        <result property="ksFansNum"    column="ks_fans_num"    />
        <result property="sphFansNum"    column="sph_fans_num"    />
        <result property="dyMasterImg"    column="dy_master_img"    />
        <result property="ksMasterImg"    column="ks_master_img"    />
        <result property="wxMasterImg"    column="wx_master_img"    />
        <result property="sphMasterImg"    column="sph_master_img"    />
        <result property="appNameType"    column="app_name_type"    />
        <result property="dyRate"    column="dy_rate"    />
        <result property="ksRate"    column="ks_rate"    />
        <result property="wxRate"    column="wx_rate"    />
        <result property="sphRate"    column="sph_rate"    />
        <result property="zsdpRate"    column="zsdp_rate"    />
        <result property="xhsRate"    column="xhs_rate"    />
        <result property="h5Rate"    column="h5_rate"    />
        <result property="pcRate"    column="pc_rate"    />
        <result property="giftCourse"    column="gift_course"    />
        <result property="wapLiveOpen"    column="wap_live_open"    />
        <result property="ddRate"    column="dd_rate"    />
        <result property="ddShopIds"    column="dd_shop_ids"    />
        <result property="deptId"    column="dept_id"    />
        <result property="userId"    column="user_id"    />
        <result property="openPromoter"    column="open_promoter"    />
        <result property="promoterNum"    column="promoter_num"    />
    </resultMap>

    <sql id="selectEnterInformationVo">
        select id, entity_type, front_path, back_path, real_name, id_number, teacher_name, business_license_company_name,
               business_license_no, business_license_path, tel_num, course_form, first_class_id, first_class_pid,
               first_class_title, first_class_douyin_class_id, second_class_id, second_class_pid, second_class_title,
               second_class_douyin_class_id, shop_nickname, shop_avatar_url, shop_desc, platform, dy_account,
               dy_uid, ks_account, wx_account, sph_account, service_begin_time, service_end_time, version, rate_type, rate,
               account_specialist, customer_service_specialist, create_time, update_time, shop_account, shop_id,
               audit_type, reject_reason, audit_time, dy_fans_num, ks_fans_num, sph_fans_num, dy_master_img, ks_master_img,
               wx_master_img, sph_master_img, app_name_type, dy_rate, ks_rate, wx_rate, sph_rate, zsdp_rate, xhs_rate, h5_rate,
               pc_rate,gift_course,wap_live_open,dd_rate,dd_shop_ids,dept_id,user_id,open_promoter,promoter_num from enter_information
    </sql>

    <select id="selectEnterInformationList" parameterType="EnterInformation" resultMap="EnterInformationResult">
        <include refid="selectEnterInformationVo"/>
        <where>  
            <if test="entityType != null "> and entity_type = #{entityType}</if>
            <if test="frontPath != null  and frontPath != ''"> and front_path = #{frontPath}</if>
            <if test="backPath != null  and backPath != ''"> and back_path = #{backPath}</if>
            <if test="realName != null  and realName != ''"> and real_name like concat('%', #{realName}, '%')</if>
            <if test="idNumber != null  and idNumber != ''"> and id_number = #{idNumber}</if>
            <if test="teacherName != null  and teacherName != ''"> and teacher_name like concat('%', #{teacherName}, '%')</if>
            <if test="businessLicenseCompanyName != null  and businessLicenseCompanyName != ''"> and business_license_company_name like concat('%', #{businessLicenseCompanyName}, '%')</if>
            <if test="businessLicenseNo != null  and businessLicenseNo != ''"> and business_license_no = #{businessLicenseNo}</if>
            <if test="businessLicensePath != null  and businessLicensePath != ''"> and business_license_path = #{businessLicensePath}</if>
            <if test="telNum != null  and telNum != ''"> and tel_num = #{telNum}</if>
            <if test="courseForm != null "> and course_form = #{courseForm}</if>
            <if test="firstClassId != null "> and first_class_id = #{firstClassId}</if>
            <if test="firstClassPid != null "> and first_class_pid = #{firstClassPid}</if>
            <if test="firstClassTitle != null  and firstClassTitle != ''"> and first_class_title = #{firstClassTitle}</if>
            <if test="firstClassDouyinClassId != null "> and first_class_douyin_class_id = #{firstClassDouyinClassId}</if>
            <if test="secondClassId != null "> and second_class_id = #{secondClassId}</if>
            <if test="secondClassPid != null "> and second_class_pid = #{secondClassPid}</if>
            <if test="secondClassTitle != null  and secondClassTitle != ''"> and second_class_title = #{secondClassTitle}</if>
            <if test="secondClassDouyinClassId != null "> and second_class_douyin_class_id = #{secondClassDouyinClassId}</if>
            <if test="shopNickname != null  and shopNickname != ''"> and shop_nickname like concat('%', #{shopNickname}, '%')</if>
            <if test="shopAvatarUrl != null  and shopAvatarUrl != ''"> and shop_avatar_url = #{shopAvatarUrl}</if>
            <if test="shopDesc != null  and shopDesc != ''"> and shop_desc = #{shopDesc}</if>
            <if test="platform != null  and platform != ''"> and platform like concat('%', #{platform}, '%')</if>
            <if test="dyAccount != null  and dyAccount != ''"> and dy_account = #{dyAccount}</if>
            <if test="dyUid != null  and dyUid != ''"> and dy_uid = #{dyUid}</if>
            <if test="ksAccount != null  and ksAccount != ''"> and ks_account = #{ksAccount}</if>
            <if test="wxAccount != null  and wxAccount != ''"> and wx_account = #{wxAccount}</if>
            <if test="sphAccount != null  and sphAccount != ''"> and sph_account = #{sphAccount}</if>
            <if test="serviceBeginTime != null "> and service_begin_time = #{serviceBeginTime}</if>
            <if test="serviceEndTime != null "> and service_end_time = #{serviceEndTime}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="rateType != null "> and rate_type = #{rateType}</if>
            <if test="rate != null "> and rate = #{rate}</if>
            <if test="accountSpecialist != null  and accountSpecialist != ''"> and account_specialist = #{accountSpecialist}</if>
            <if test="customerServiceSpecialist != null  and customerServiceSpecialist != ''"> and customer_service_specialist = #{customerServiceSpecialist}</if>
            <if test="shopAccount != null  and shopAccount != ''"> and shop_account = #{shopAccount}</if>
            <if test="shopId != null "> and shop_id = #{shopId}</if>
            <if test="auditType != null "> and audit_type = #{auditType}</if>
            <if test="rejectReason != null  and rejectReason != ''"> and reject_reason = #{rejectReason}</if>
            <if test="auditTime != null "> and audit_time = #{auditTime}</if>
            <if test="dyFansNum != null "> and dy_fans_num = #{dyFansNum}</if>
            <if test="ksFansNum != null "> and ks_fans_num = #{ksFansNum}</if>
            <if test="sphFansNum != null "> and sph_fans_num = #{sphFansNum}</if>
            <if test="dyMasterImg != null  and dyMasterImg != ''"> and dy_master_img = #{dyMasterImg}</if>
            <if test="ksMasterImg != null  and ksMasterImg != ''"> and ks_master_img = #{ksMasterImg}</if>
            <if test="wxMasterImg != null  and wxMasterImg != ''"> and wx_master_img = #{wxMasterImg}</if>
            <if test="sphMasterImg != null  and sphMasterImg != ''"> and sph_master_img = #{sphMasterImg}</if>
            <if test="appNameType != null "> and app_name_type = #{appNameType}</if>
            <if test="dyRate != null "> and dy_rate = #{dyRate}</if>
            <if test="ksRate != null "> and ks_rate = #{ksRate}</if>
            <if test="wxRate != null "> and wx_rate = #{wxRate}</if>
            <if test="sphRate != null "> and sph_rate = #{sphRate}</if>
            <if test="zsdpRate != null "> and zsdp_rate = #{zsdpRate}</if>
            <if test="xhsRate != null "> and xhs_rate = #{xhsRate}</if>
            <if test="h5Rate != null "> and h5_rate = #{h5Rate}</if>
            <if test="pcRate != null "> and pc_rate = #{pcRate}</if>
            <if test="giftCourse != null "> and gift_course = #{giftCourse}</if>
            <if test="wapLiveOpen != null "> and wap_live_open = #{wapLiveOpen}</if>
            <if test="ddRate != null "> and dd_rate = #{ddRate}</if>
            <if test="ddShopIds != null  and ddShopIds != ''"> and dd_shop_ids = #{ddShopIds}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="openPromoter != null "> and open_promoter = #{openPromoter}</if>
            <if test="promoterNum != null "> and promoter_num = #{promoterNum}</if>
            <if test="isAgent != null and isAgent==1 "> and ( dept_id is not null or user_id is not null ) </if>
            <if test="searchValue != null  and searchValue != ''">
             and
                (
                shop_nickname like concat('%', #{searchValue}, '%')
                or shop_account like concat('%', #{searchValue}, '%')
                or shop_id like concat('%', #{searchValue}, '%')
                )
             </if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND create_time &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND create_time &lt;= #{params.endTime}
            </if>

        </where>
        order by update_time desc
    </select>
    
    <select id="selectEnterInformationById" parameterType="Long" resultMap="EnterInformationResult">
        <include refid="selectEnterInformationVo"/>
        where id = #{id}
    </select>
    <select id="selectEnterInformationByShopId" parameterType="Long" resultMap="EnterInformationResult">
        <include refid="selectEnterInformationVo"/>
        where shop_id = #{shopId}
    </select>

    <insert id="insertEnterInformation" parameterType="EnterInformation" useGeneratedKeys="true" keyProperty="id">
        <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into enter_information
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="entityType != null">entity_type,</if>
            <if test="frontPath != null">front_path,</if>
            <if test="backPath != null">back_path,</if>
            <if test="realName != null">real_name,</if>
            <if test="idNumber != null">id_number,</if>
            <if test="teacherName != null">teacher_name,</if>
            <if test="businessLicenseCompanyName != null">business_license_company_name,</if>
            <if test="businessLicenseNo != null">business_license_no,</if>
            <if test="businessLicensePath != null">business_license_path,</if>
            <if test="telNum != null">tel_num,</if>
            <if test="courseForm != null">course_form,</if>
            <if test="firstClassId != null">first_class_id,</if>
            <if test="firstClassPid != null">first_class_pid,</if>
            <if test="firstClassTitle != null">first_class_title,</if>
            <if test="firstClassDouyinClassId != null">first_class_douyin_class_id,</if>
            <if test="secondClassId != null">second_class_id,</if>
            <if test="secondClassPid != null">second_class_pid,</if>
            <if test="secondClassTitle != null">second_class_title,</if>
            <if test="secondClassDouyinClassId != null">second_class_douyin_class_id,</if>
            <if test="shopNickname != null">shop_nickname,</if>
            <if test="shopAvatarUrl != null">shop_avatar_url,</if>
            <if test="shopDesc != null">shop_desc,</if>
            <if test="platform != null">platform,</if>
            <if test="dyAccount != null">dy_account,</if>
            <if test="dyUid != null">dy_uid,</if>
            <if test="ksAccount != null">ks_account,</if>
            <if test="wxAccount != null">wx_account,</if>
            <if test="sphAccount != null">sph_account,</if>
            <if test="serviceBeginTime != null">service_begin_time,</if>
            <if test="serviceEndTime != null">service_end_time,</if>
            <if test="version != null">version,</if>
            <if test="rateType != null">rate_type,</if>
            <if test="rate != null">rate,</if>
            <if test="accountSpecialist != null">account_specialist,</if>
            <if test="customerServiceSpecialist != null">customer_service_specialist,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="shopAccount != null">shop_account,</if>
            <if test="shopId != null">shop_id,</if>
            <if test="auditType != null">audit_type,</if>
            <if test="rejectReason != null">reject_reason,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="dyFansNum != null">dy_fans_num,</if>
            <if test="ksFansNum != null">ks_fans_num,</if>
            <if test="sphFansNum != null">sph_fans_num,</if>
            <if test="dyMasterImg != null">dy_master_img,</if>
            <if test="ksMasterImg != null">ks_master_img,</if>
            <if test="wxMasterImg != null">wx_master_img,</if>
            <if test="sphMasterImg != null">sph_master_img,</if>
            <if test="appNameType != null">app_name_type,</if>
            <if test="dyRate != null">dy_rate,</if>
            <if test="ksRate != null">ks_rate,</if>
            <if test="wxRate != null">wx_rate,</if>
            <if test="sphRate != null">sph_rate,</if>
            <if test="zsdpRate != null">zsdp_rate,</if>
            <if test="xhsRate != null">xhs_rate,</if>
            <if test="h5Rate != null">h5_rate,</if>
            <if test="pcRate != null">pc_rate,</if>
            <if test="giftCourse != null">gift_course,</if>
            <if test="wapLiveOpen != null">wap_live_open,</if>
            <if test="ddRate != null">dd_rate,</if>
            <if test="ddShopIds != null">dd_shop_ids,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="openPromoter != null">open_promoter,</if>
            <if test="promoterNum != null">promoter_num,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="entityType != null">#{entityType},</if>
            <if test="frontPath != null">#{frontPath},</if>
            <if test="backPath != null">#{backPath},</if>
            <if test="realName != null">#{realName},</if>
            <if test="idNumber != null">#{idNumber},</if>
            <if test="teacherName != null">#{teacherName},</if>
            <if test="businessLicenseCompanyName != null">#{businessLicenseCompanyName},</if>
            <if test="businessLicenseNo != null">#{businessLicenseNo},</if>
            <if test="businessLicensePath != null">#{businessLicensePath},</if>
            <if test="telNum != null">#{telNum},</if>
            <if test="courseForm != null">#{courseForm},</if>
            <if test="firstClassId != null">#{firstClassId},</if>
            <if test="firstClassPid != null">#{firstClassPid},</if>
            <if test="firstClassTitle != null">#{firstClassTitle},</if>
            <if test="firstClassDouyinClassId != null">#{firstClassDouyinClassId},</if>
            <if test="secondClassId != null">#{secondClassId},</if>
            <if test="secondClassPid != null">#{secondClassPid},</if>
            <if test="secondClassTitle != null">#{secondClassTitle},</if>
            <if test="secondClassDouyinClassId != null">#{secondClassDouyinClassId},</if>
            <if test="shopNickname != null">#{shopNickname},</if>
            <if test="shopAvatarUrl != null">#{shopAvatarUrl},</if>
            <if test="shopDesc != null">#{shopDesc},</if>
            <if test="platform != null">#{platform},</if>
            <if test="dyAccount != null">#{dyAccount},</if>
            <if test="dyUid != null">#{dyUid},</if>
            <if test="ksAccount != null">#{ksAccount},</if>
            <if test="wxAccount != null">#{wxAccount},</if>
            <if test="sphAccount != null">#{sphAccount},</if>
            <if test="serviceBeginTime != null">#{serviceBeginTime},</if>
            <if test="serviceEndTime != null">#{serviceEndTime},</if>
            <if test="version != null">#{version},</if>
            <if test="rateType != null">#{rateType},</if>
            <if test="rate != null">#{rate},</if>
            <if test="accountSpecialist != null">#{accountSpecialist},</if>
            <if test="customerServiceSpecialist != null">#{customerServiceSpecialist},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="shopAccount != null">#{shopAccount},</if>
            <if test="shopId != null">#{shopId},</if>
            <if test="auditType != null">#{auditType},</if>
            <if test="rejectReason != null">#{rejectReason},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="dyFansNum != null">#{dyFansNum},</if>
            <if test="ksFansNum != null">#{ksFansNum},</if>
            <if test="sphFansNum != null">#{sphFansNum},</if>
            <if test="dyMasterImg != null">#{dyMasterImg},</if>
            <if test="ksMasterImg != null">#{ksMasterImg},</if>
            <if test="wxMasterImg != null">#{wxMasterImg},</if>
            <if test="sphMasterImg != null">#{sphMasterImg},</if>
            <if test="appNameType != null">#{appNameType},</if>
            <if test="dyRate != null">#{dyRate},</if>
            <if test="ksRate != null">#{ksRate},</if>
            <if test="wxRate != null">#{wxRate},</if>
            <if test="sphRate != null">#{sphRate},</if>
            <if test="zsdpRate != null">#{zsdpRate},</if>
            <if test="xhsRate != null">#{xhsRate},</if>
            <if test="h5Rate != null">#{h5Rate},</if>
            <if test="pcRate != null">#{pcRate},</if>
            <if test="giftCourse != null">#{giftCourse},</if>
            <if test="wapLiveOpen != null">#{wapLiveOpen},</if>
            <if test="ddRate != null">#{ddRate},</if>
            <if test="ddShopIds != null">#{ddShopIds},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="openPromoter != null">#{openPromoter},</if>
            <if test="promoterNum != null">#{promoterNum},</if>
         </trim>
    </insert>
    <insert id="insertTTeacher" parameterType="com.ruoyi.system.dto.TeacherDTO">
        insert into `wendao101-teacher`.t_teacher
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="unionId != null">union_id,</if>
            <if test="openId != null">open_id,</if>
            <if test="shopName != null">shop_name,</if>
            <if test="mobile != null">mobile,</if>
            <if test="loginNum != null">login_num,</if>
            <if test="password != null">password,</if>
            <if test="teacherName != null">teacher_name,</if>
            <if test="teacherDesc != null">teacher_desc,</if>
            <if test="avatarUrl != null">avatar_url,</if>
            <if test="platform != null">platform,</if>
            <if test="hot != null">hot,</if>
            <if test="loginIp != null">login_ip,</if>
            <if test="loginDate != null">login_date,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="dyUid != null">dy_uid,</if>
            <if test="gender != null">gender,</if>
            <if test="version != null">version,</if>
            <if test="endDate != null">end_date,</if>
            <if test="appNameType != null">app_name_type,</if>
            <if test="knowledgeStoreDomain != null">knowledge_store_domain,</if>
            <if test="knowledgeStoreName != null">knowledge_store_name,</if>
            <if test="knowledgeStoreDesc != null">knowledge_store_desc,</if>
            <if test="knowledgeStoreAvatarUrl != null">knowledge_store_avatar_url,</if>

            <if test="giftCourse != null">gift_course,</if>
            <if test="wapLiveOpen != null">wap_live_open,</if>
            <if test="ddShopIds != null">dd_shop_ids,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="openPromoter != null">open_promoter,</if>
            <if test="promoterNum != null">promoter_num,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="unionId != null">#{unionId},</if>
            <if test="openId != null">#{openId},</if>
            <if test="shopName != null">#{shopName},</if>
            <if test="mobile != null">#{mobile},</if>
            <if test="loginNum != null">#{loginNum},</if>
            <if test="password != null">#{password},</if>
            <if test="teacherName != null">#{teacherName},</if>
            <if test="teacherDesc != null">#{teacherDesc},</if>
            <if test="avatarUrl != null">#{avatarUrl},</if>
            <if test="platform != null">#{platform},</if>
            <if test="hot != null">#{hot},</if>
            <if test="loginIp != null">#{loginIp},</if>
            <if test="loginDate != null">#{loginDate},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="dyUid != null">#{dyUid},</if>
            <if test="gender != null">#{gender},</if>
            <if test="version != null">#{version},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="appNameType != null">#{appNameType},</if>
            <if test="knowledgeStoreDomain != null">#{knowledgeStoreDomain},</if>
            <if test="knowledgeStoreName != null">#{knowledgeStoreName},</if>
            <if test="knowledgeStoreDesc != null">#{knowledgeStoreDesc},</if>
            <if test="knowledgeStoreAvatarUrl != null">#{knowledgeStoreAvatarUrl},</if>

            <if test="giftCourse != null">#{giftCourse},</if>
            <if test="wapLiveOpen != null">#{wapLiveOpen},</if>
            <if test="ddShopIds != null">#{ddShopIds},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="openPromoter != null">#{openPromoter},</if>
            <if test="promoterNum != null">#{promoterNum},</if>
        </trim>
    </insert>
    <insert id="insertSmsRecord">
        insert into `wendao101-teacher`.t_sms (code,code_index_key,phone_number) values (#{code},#{codeIndexKey},#{phoneNumber})
    </insert>
    <insert id="insertTeacherResourcePkg">
        insert into `wendao101-order`.teacher_resource_package
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="resourcePkgId != null">resource_pkg_id,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="resourcePkgName != null">resource_pkg_name,</if>
            <if test="pkgPrice != null">pkg_price,</if>
            <if test="validityPeriod != null">validity_period,</if>
            <if test="resourceCount != null">resource_count,</if>
            <if test="remainingCount != null">remaining_count,</if>
            <if test="isEnable != null">is_enable,</if>
            <if test="featureCode != null">feature_code,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="resourcePkgId != null">#{resourcePkgId},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="resourcePkgName != null">#{resourcePkgName},</if>
            <if test="pkgPrice != null">#{pkgPrice},</if>
            <if test="validityPeriod != null">#{validityPeriod},</if>
            <if test="resourceCount != null">#{resourceCount},</if>
            <if test="remainingCount != null">#{remainingCount},</if>
            <if test="isEnable != null">#{isEnable},</if>
            <if test="featureCode != null">#{featureCode},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
        </trim>
    </insert>

    <update id="updateEnterInformation" parameterType="EnterInformation">
        update enter_information
        <trim prefix="SET" suffixOverrides=",">
            <if test="entityType != null">entity_type = #{entityType},</if>
            <if test="frontPath != null">front_path = #{frontPath},</if>
            <if test="backPath != null">back_path = #{backPath},</if>
            <if test="realName != null">real_name = #{realName},</if>
            <if test="idNumber != null">id_number = #{idNumber},</if>
            <if test="teacherName != null">teacher_name = #{teacherName},</if>
            <if test="businessLicenseCompanyName != null">business_license_company_name = #{businessLicenseCompanyName},</if>
            <if test="businessLicenseNo != null">business_license_no = #{businessLicenseNo},</if>
            <if test="businessLicensePath != null">business_license_path = #{businessLicensePath},</if>
            <if test="telNum != null">tel_num = #{telNum},</if>
            <if test="courseForm != null">course_form = #{courseForm},</if>
            <if test="firstClassId != null">first_class_id = #{firstClassId},</if>
            <if test="firstClassPid != null">first_class_pid = #{firstClassPid},</if>
            <if test="firstClassTitle != null">first_class_title = #{firstClassTitle},</if>
            <if test="firstClassDouyinClassId != null">first_class_douyin_class_id = #{firstClassDouyinClassId},</if>
            <if test="secondClassId != null">second_class_id = #{secondClassId},</if>
            <if test="secondClassPid != null">second_class_pid = #{secondClassPid},</if>
            <if test="secondClassTitle != null">second_class_title = #{secondClassTitle},</if>
            <if test="secondClassDouyinClassId != null">second_class_douyin_class_id = #{secondClassDouyinClassId},</if>
            <if test="shopNickname != null">shop_nickname = #{shopNickname},</if>
            <if test="shopAvatarUrl != null">shop_avatar_url = #{shopAvatarUrl},</if>
            <if test="shopDesc != null">shop_desc = #{shopDesc},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="dyAccount != null">dy_account = #{dyAccount},</if>
            <if test="dyUid != null">dy_uid = #{dyUid},</if>
            <if test="ksAccount != null">ks_account = #{ksAccount},</if>
            <if test="wxAccount != null">wx_account = #{wxAccount},</if>
            <if test="sphAccount != null">sph_account = #{sphAccount},</if>
            <if test="serviceBeginTime != null">service_begin_time = #{serviceBeginTime},</if>
            <if test="serviceEndTime != null">service_end_time = #{serviceEndTime},</if>
            <if test="version != null">version = #{version},</if>
            <if test="rateType != null">rate_type = #{rateType},</if>
            <if test="rate != null">rate = #{rate},</if>
            <if test="accountSpecialist != null">account_specialist = #{accountSpecialist},</if>
            <if test="customerServiceSpecialist != null">customer_service_specialist = #{customerServiceSpecialist},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="shopAccount != null">shop_account = #{shopAccount},</if>
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="auditType != null">audit_type = #{auditType},</if>
            <if test="rejectReason != null">reject_reason = #{rejectReason},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="dyFansNum != null">dy_fans_num = #{dyFansNum},</if>
            <if test="ksFansNum != null">ks_fans_num = #{ksFansNum},</if>
            <if test="sphFansNum != null">sph_fans_num = #{sphFansNum},</if>
            <if test="dyMasterImg != null">dy_master_img = #{dyMasterImg},</if>
            <if test="ksMasterImg != null">ks_master_img = #{ksMasterImg},</if>
            <if test="wxMasterImg != null">wx_master_img = #{wxMasterImg},</if>
            <if test="sphMasterImg != null">sph_master_img = #{sphMasterImg},</if>
            <if test="appNameType != null">app_name_type = #{appNameType},</if>
            <if test="dyRate != null">dy_rate = #{dyRate},</if>
            <if test="ksRate != null">ks_rate = #{ksRate},</if>
            <if test="wxRate != null">wx_rate = #{wxRate},</if>
            <if test="sphRate != null">sph_rate = #{sphRate},</if>
            <if test="zsdpRate != null">zsdp_rate = #{zsdpRate},</if>
            <if test="xhsRate != null">xhs_rate = #{xhsRate},</if>
            <if test="h5Rate != null">h5_rate = #{h5Rate},</if>
            <if test="pcRate != null">pc_rate = #{pcRate},</if>
            <if test="giftCourse != null">gift_course = #{giftCourse},</if>
            <if test="wapLiveOpen != null">wap_live_open = #{wapLiveOpen},</if>
            <if test="ddRate != null">dd_rate = #{ddRate},</if>
            <if test="ddShopIds != null">dd_shop_ids = #{ddShopIds},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="openPromoter != null">open_promoter = #{openPromoter},</if>
            <if test="promoterNum != null">promoter_num = #{promoterNum},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateTeacherEnterStatus">
        update `wendao101-teacher`.teacher_enter
        <set>
            <if test="handleName != null and handleName != ''">handle_name = #{handleName},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
        </set>
        where id = #{id}
    </update>
    <update id="updateTeacherPlatfrom">
        update `wendao101-teacher`.t_teacher
        <set>
            <if test="platform != null and platform != ''">platform = #{platform},</if>
            <if test="version != null">version = #{version},</if>
            <if test="giftCourse != null">gift_course = #{giftCourse},</if>
            <if test="wapLiveOpen != null">wap_live_open = #{wapLiveOpen},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="openPromoter != null">open_promoter = #{openPromoter},</if>
            <if test="promoterNum != null">promoter_num = #{promoterNum},</if>
            dd_shop_ids = #{ddShopIds},
        </set>
        where teacher_id=#{teacherId}
    </update>
    <update id="updateTeacherMobile" parameterType="com.ruoyi.system.dto.ChangeShopMobileDTO">
        update `wendao101-teacher`.t_teacher
        set mobile = #{newTelNum}
        where mobile = #{oldTelNum} and app_name_type = #{appNameType}
    </update>
    <update id="updateSysUserByNewMobile" parameterType="com.ruoyi.system.dto.ChangeShopMobileDTO">
        update `wendao101-cloud`.sys_user
        set user_name = #{newTelNum},nick_name=#{newTelNum}
        where user_name = #{oldTelNum} and app_name_type = #{appNameType}
    </update>
    <update id="updateEnterInformationTelNum" parameterType="com.ruoyi.system.dto.ChangeShopMobileDTO">
        update enter_information
        set tel_num = #{newTelNum},shop_account=#{newTelNum}
        where tel_num = #{oldTelNum} and app_name_type = #{appNameType}
    </update>
    <update id="updateTeacherFroKnowledgeStore">
        update `wendao101-teacher`.t_teacher
        set mobile = #{mobile},
            platform = #{platform},
            version = #{version},
            knowledge_store_domain=#{knowledgeStoreDomain},
            knowledge_store_name=#{knowledgeStoreName},
            knowledge_store_desc=#{knowledgeStoreDesc},
            knowledge_store_avatar_url=#{knowledgeStoreAvatarUrl},
            gift_course = #{giftCourse},
            wap_live_open = #{wapLiveOpen},
            dd_shop_ids = #{ddShopIds}
        <if test="endDate != null">,end_date = #{endDate}</if>
        <if test="openPromoter != null">,open_promoter = #{openPromoter}</if>
        <if test="promoterNum != null">,promoter_num = #{promoterNum}</if>
        where teacher_id = #{teacherId}
    </update>

    <delete id="deleteEnterInformationById" parameterType="Long">
        delete from enter_information where id = #{id}
    </delete>

    <delete id="deleteEnterInformationByIds" parameterType="String">
        delete from enter_information where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectAll" parameterType="com.ruoyi.system.domain.vo.EnterInformationVO" resultMap="EnterInformationResult">
        <include refid="selectEnterInformationVo"/>
        <where>
            <if test="entityType != null "> and entity_type = #{entityType}</if>
            <if test="realName != null  and realName != ''"> and real_name like concat('%', #{realName}, '%')</if>
            <if test="telNum != null  and telNum != ''"> and tel_num = #{telNum}</if>
            <if test="courseForm != null "> and course_form = #{courseForm}</if>
            <if test="firstClassId != null "> and first_class_id = #{firstClassId}</if>
            <if test="firstClassPid != null "> and first_class_pid = #{firstClassPid}</if>
            <if test="firstClassTitle != null  and firstClassTitle != ''"> and first_class_title = #{firstClassTitle}</if>
            <if test="firstClassDouyinClassId != null "> and first_class_douyin_class_id = #{firstClassDouyinClassId}</if>
            <if test="secondClassId != null "> and second_class_id = #{secondClassId}</if>
            <if test="secondClassPid != null "> and second_class_pid = #{secondClassPid}</if>
            <if test="secondClassTitle != null  and secondClassTitle != ''"> and second_class_title = #{secondClassTitle}</if>
            <if test="secondClassDouyinClassId != null "> and second_class_douyin_class_id = #{secondClassDouyinClassId}</if>
            <if test="auditType != null "> and audit_type = #{auditType}</if>
            <if test="appNameType != null "> and app_name_type = #{appNameType}</if>
            <if test="giftCourse != null "> and gift_course = #{giftCourse}</if>
            <if test="wapLiveOpen != null "> and wap_live_open = #{wapLiveOpen}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="isAgent != null and isAgent==1"> and (dept_id is not null or user_id is not null) </if>
            <if test="beginTime != null">
                and create_time &gt;= #{beginTime}
            </if>
            <if test="endTime != null">
                and create_time &lt;= #{endTime}
            </if>
        </where>
        <if test="auditType != 1">
            ORDER BY create_time desc
        </if>

    </select>

    <select id="getEnterInformationList" parameterType="EnterInformation" resultMap="EnterInformationResult">
        <include refid="selectEnterInformationVo"/>
        <where>
            <if test="telNum != null  and telNum != ''"> and tel_num = #{telNum}</if>
            <if test="idNumber != null  and idNumber != ''"> and id_number = #{idNumber}</if>
            <if test="appNameType != null  "> and app_name_type = #{appNameType}</if>
            and audit_type != 2
        </where>
    </select>

    <select id="getTeacherEnterList" parameterType="com.ruoyi.system.domain.vo.TeacherEnterVO" resultType="com.ruoyi.system.domain.TeacherEnter">
        select a.id id
             , a.open_id openId
             , a.teacher_enter_name teacherEnterName
             , a.teacher_enter_phone teacherEnterPhone
             , a.app_platform appPlatform
             , a.teacher_enter_dy teacherEnterDy
             , a.fans_num fansNum
             , a.Invitation_code InvitationCode
             , a.category_type categoryType
             , a.teacher_enter_type teacherEnterType
             , a.is_experience isExperience
             , a.is_course isCourse
             , a.is_delete isDelete
             , a.handle_name handleName
             , a.status status
             , a.create_time createTime
             , a.update_time updateTime
             ,a.app_name_type appNameType
             ,a.wendao_promoter_open_id wendaoPromoterOpenId
            ,b.nick_name wendaoPromoterNickName
        from `wendao101-teacher`.teacher_enter a left join `wendao101-teacher`.wendao_user b on a.wendao_promoter_open_id = b.open_id
        <where>
            <if test="appPlatform != null ">
                and a.app_Platform = #{appPlatform}
            </if>
            <if test="teacherEnterName != null  and teacherEnterName != ''">
                and a.teacher_enter_name like concat('%', #{teacherEnterName}, '%')
            </if>
            <if test="teacherEnterPhone != null ">
                and a.teacher_enter_phone = #{teacherEnterPhone}
            </if>
            <if test="categoryType != null ">
                and a.category_type = #{categoryType}
            </if>
            <if test="teacherEnterType != null ">
                and a.teacher_enter_type = #{teacherEnterType}
            </if>
            <if test="status != null">
                and a.status = #{status}
            </if>
            <if test="beginTime != null">
                and a.create_time &gt;= #{beginTime}
            </if>
            <if test="beginTime != null">
                and a.create_time &gt;= #{beginTime}
            </if>
            <if test="handleName != null  and handleName != ''">
                and a.handle_name like concat('%', #{handleName}, '%')
            </if>
            <if test="appNameType != null">
                and a.app_name_type = #{appNameType}
            </if>
                and a.is_delete = 0
        </where>
        ORDER BY a.create_time desc
    </select>

    <select id="selectDyMiniClassList" resultType="com.ruoyi.system.domain.dto.DyMiniClassDTO">
        select id id, class_name className
        from `wendao101-douyin`.dy_mini_class
    </select>
    <select id="selectTeacherInfoByAppNameTypeAndTelNum"  parameterType="EnterInformation" resultType="com.ruoyi.system.dto.PasswordInfoDTO">
        select teacher_id  shopId,shop_name shopName,password
        from `wendao101-teacher`.t_teacher where mobile=#{telNum} and app_name_type=#{appNameType} limit 1
    </select>
    <select id="selectCountTeacherInfo" resultType="java.lang.Integer">
        select count(*) from `wendao101-teacher`.t_teacher where mobile=#{telNum} and app_name_type=#{appNameType}
    </select>
    <select id="countSmsRecord" parameterType="com.ruoyi.system.dto.ChangeShopMobileDTO" resultType="java.lang.Integer">
        select count(*) from `wendao101-teacher`.t_sms where code=#{code} and code_index_key=#{uuid} and phone_number=#{masterPhoneNumber}
    </select>
    <select id="selectSopIdForCheckSubAccount" resultType="java.lang.Long">
        select teacher_id from `wendao101-teacher`.teacher_sub_account where user_name=#{telNum} and app_name_type=#{appNameType} and is_delete=0 limit 1
    </select>
    <select id="selectTeacherKnownledgeStoreDomain" resultType="com.ruoyi.system.dto.TeacherDTO">
        select teacher_id as teacherId,
               platform as platform,
               knowledge_store_domain as knowledgeStoreDomain,
               knowledge_store_name as knowledgeStoreName,
               knowledge_store_desc as knowledgeStoreDesc,
               knowledge_store_avatar_url as knowledgeStoreAvatarUrl
        from `wendao101-teacher`.t_teacher
        where teacher_id=#{teacherId}
    </select>

    <!-- 根据时间范围、客户专员和审核类型查询入驻信息 -->
    <select id="queryByTimeAndSpecialist" resultType="com.ruoyi.system.domain.dto.EnterInformationStatDTO">
        SELECT
            ei.id,
            ei.shop_id as shopId,
            ei.version,
            ei.gift_course as giftCourse,
            ei.wap_live_open as wapLiveOpen,
            ei.open_promoter as openPromoter,
            ei.promoter_num as promoterNum,
            ei.update_time as updateTime,
            ei.account_specialist as accountSpecialist
        FROM enter_information ei
        WHERE ei.audit_type = #{auditType}
        <if test="accountSpecialist != null and accountSpecialist != ''">
            AND ei.account_specialist = #{accountSpecialist}
        </if>
        <if test="startTime != null">
            AND ei.update_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND ei.update_time &lt; #{endTime}
        </if>
        ORDER BY ei.update_time DESC
    </select>

    <!-- 根据时间范围和审核类型统计开户数量 -->
    <select id="countByTimeAndAuditType" resultType="int">
        SELECT COUNT(1)
        FROM enter_information ei
        WHERE ei.audit_type = #{auditType}
        <if test="startTime != null">
            AND ei.update_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND ei.update_time &lt; #{endTime}
        </if>
    </select>

    <!-- 根据时间范围和订单状态统计订单交易总额 -->
    <select id="sumOrderAmountByTimeAndStatus" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(co.pay_price), 0)
        FROM `wendao101-order`.course_order co
        WHERE co.order_status = #{orderStatus}
        <if test="startTime != null">
            AND co.order_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND co.order_time &lt; #{endTime}
        </if>
    </select>

    <!-- 根据时间范围、客户专员和审核类型统计开户数量 -->
    <select id="countByTimeAndSpecialist" resultType="int">
        SELECT COUNT(1)
        FROM enter_information ei
        WHERE ei.audit_type = #{auditType}
        <if test="accountSpecialist != null and accountSpecialist != ''">
            AND ei.account_specialist = #{accountSpecialist}
        </if>
        <if test="startTime != null">
            AND ei.update_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND ei.update_time &lt; #{endTime}
        </if>
    </select>

    <!-- 根据时间范围、员工昵称列表和审核类型查询开户排行 -->
    <select id="queryOpenAccountRankingByNickNames" resultType="java.util.Map">
        SELECT
            ei.account_specialist as nickName,
            COUNT(1) as openAccountCount
        FROM enter_information ei
        WHERE ei.audit_type = #{auditType}
        <if test="nickNames != null and nickNames.size() > 0">
            AND ei.account_specialist IN
            <foreach collection="nickNames" item="nickName" open="(" separator="," close=")">
                #{nickName}
            </foreach>
        </if>
        <if test="startTime != null">
            AND ei.update_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND ei.update_time &lt; #{endTime}
        </if>
        GROUP BY ei.account_specialist
        ORDER BY COUNT(1) DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

</mapper>