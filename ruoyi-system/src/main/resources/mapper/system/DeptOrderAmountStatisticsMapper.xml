<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DeptOrderAmountStatisticsMapper">
    
    <resultMap type="DeptOrderAmountStatistics" id="DeptOrderAmountStatisticsResult">
        <result property="id"    column="id"    />
        <result property="statType"    column="stat_type"    />
        <result property="deptName"    column="dept_name"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="statDate"    column="stat_date"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDeptOrderAmountStatisticsVo">
        select id, stat_type, dept_name, total_amount, stat_date, create_time, update_time from dept_order_amount_statistics
    </sql>

    <select id="selectDeptOrderAmountStatisticsList" parameterType="DeptOrderAmountStatistics" resultMap="DeptOrderAmountStatisticsResult">
        <include refid="selectDeptOrderAmountStatisticsVo"/>
        <where>  
            <if test="statType != null  and statType != ''"> and stat_type = #{statType}</if>
            <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="totalAmount != null "> and total_amount = #{totalAmount}</if>
            <if test="statDate != null "> and stat_date = #{statDate}</if>
        </where>
    </select>
    
    <select id="selectDeptOrderAmountStatisticsById" parameterType="Long" resultMap="DeptOrderAmountStatisticsResult">
        <include refid="selectDeptOrderAmountStatisticsVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertDeptOrderAmountStatistics" parameterType="DeptOrderAmountStatistics" useGeneratedKeys="true" keyProperty="id">
        insert into dept_order_amount_statistics
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="statType != null and statType != ''">stat_type,</if>
            <if test="deptName != null and deptName != ''">dept_name,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="statDate != null">stat_date,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="statType != null and statType != ''">#{statType},</if>
            <if test="deptName != null and deptName != ''">#{deptName},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="statDate != null">#{statDate},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDeptOrderAmountStatistics" parameterType="DeptOrderAmountStatistics">
        update dept_order_amount_statistics
        <trim prefix="SET" suffixOverrides=",">
            <if test="statType != null and statType != ''">stat_type = #{statType},</if>
            <if test="deptName != null and deptName != ''">dept_name = #{deptName},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="statDate != null">stat_date = #{statDate},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDeptOrderAmountStatisticsById" parameterType="Long">
        delete from dept_order_amount_statistics where id = #{id}
    </delete>

    <delete id="deleteDeptOrderAmountStatisticsByIds" parameterType="String">
        delete from dept_order_amount_statistics where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 批量插入 -->
    <insert id="batchInsertDeptOrderAmountStatistics" parameterType="java.util.List">
        insert into dept_order_amount_statistics (stat_type, dept_name, total_amount, stat_date, create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.statType}, #{item.deptName}, #{item.totalAmount}, #{item.statDate}, #{item.createTime}, #{item.updateTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
        total_amount = VALUES(total_amount),
        update_time = VALUES(update_time)
    </insert>

    <!-- 根据统计类型和统计日期删除数据 -->
    <delete id="deleteByStatTypeAndDate">
        delete from dept_order_amount_statistics 
        where stat_type = #{statType} and stat_date = #{statDate}
    </delete>

</mapper>
