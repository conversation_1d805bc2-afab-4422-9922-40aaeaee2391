package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 部门订单交易额统计对象 dept_order_amount_statistics
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public class DeptOrderAmountStatistics extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 统计类型：daily-昨天,weekly-7天,monthly-本月,yearly-本年 */
    @Excel(name = "统计类型")
    private String statType;

    /** 部门名称，其他交易额用"其他"表示 */
    @Excel(name = "部门名称")
    private String deptName;

    /** 部门交易总额 */
    @Excel(name = "部门交易总额")
    private BigDecimal totalAmount;

    /** 统计日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "统计日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date statDate;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setStatType(String statType) 
    {
        this.statType = statType;
    }

    public String getStatType() 
    {
        return statType;
    }

    public void setDeptName(String deptName) 
    {
        this.deptName = deptName;
    }

    public String getDeptName() 
    {
        return deptName;
    }

    public void setTotalAmount(BigDecimal totalAmount) 
    {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getTotalAmount() 
    {
        return totalAmount;
    }

    public void setStatDate(Date statDate) 
    {
        this.statDate = statDate;
    }

    public Date getStatDate() 
    {
        return statDate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("statType", getStatType())
            .append("deptName", getDeptName())
            .append("totalAmount", getTotalAmount())
            .append("statDate", getStatDate())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
