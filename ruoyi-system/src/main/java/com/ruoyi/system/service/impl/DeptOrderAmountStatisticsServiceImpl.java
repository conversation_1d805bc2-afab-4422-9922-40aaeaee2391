package com.ruoyi.system.service.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.DeptOrderAmountStatisticsMapper;
import com.ruoyi.system.domain.DeptOrderAmountStatistics;
import com.ruoyi.system.service.IDeptOrderAmountStatisticsService;

/**
 * 部门订单交易额统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Service
public class DeptOrderAmountStatisticsServiceImpl implements IDeptOrderAmountStatisticsService 
{
    @Autowired
    private DeptOrderAmountStatisticsMapper deptOrderAmountStatisticsMapper;

    /**
     * 查询部门订单交易额统计
     * 
     * @param id 部门订单交易额统计主键
     * @return 部门订单交易额统计
     */
    @Override
    public DeptOrderAmountStatistics selectDeptOrderAmountStatisticsById(Long id)
    {
        return deptOrderAmountStatisticsMapper.selectDeptOrderAmountStatisticsById(id);
    }

    /**
     * 查询部门订单交易额统计列表
     * 
     * @param deptOrderAmountStatistics 部门订单交易额统计
     * @return 部门订单交易额统计
     */
    @Override
    public List<DeptOrderAmountStatistics> selectDeptOrderAmountStatisticsList(DeptOrderAmountStatistics deptOrderAmountStatistics)
    {
        return deptOrderAmountStatisticsMapper.selectDeptOrderAmountStatisticsList(deptOrderAmountStatistics);
    }

    /**
     * 新增部门订单交易额统计
     * 
     * @param deptOrderAmountStatistics 部门订单交易额统计
     * @return 结果
     */
    @Override
    public int insertDeptOrderAmountStatistics(DeptOrderAmountStatistics deptOrderAmountStatistics)
    {
        return deptOrderAmountStatisticsMapper.insertDeptOrderAmountStatistics(deptOrderAmountStatistics);
    }

    /**
     * 修改部门订单交易额统计
     * 
     * @param deptOrderAmountStatistics 部门订单交易额统计
     * @return 结果
     */
    @Override
    public int updateDeptOrderAmountStatistics(DeptOrderAmountStatistics deptOrderAmountStatistics)
    {
        return deptOrderAmountStatisticsMapper.updateDeptOrderAmountStatistics(deptOrderAmountStatistics);
    }

    /**
     * 批量删除部门订单交易额统计
     * 
     * @param ids 需要删除的部门订单交易额统计主键
     * @return 结果
     */
    @Override
    public int deleteDeptOrderAmountStatisticsByIds(Long[] ids)
    {
        return deptOrderAmountStatisticsMapper.deleteDeptOrderAmountStatisticsByIds(ids);
    }

    /**
     * 删除部门订单交易额统计信息
     * 
     * @param id 部门订单交易额统计主键
     * @return 结果
     */
    @Override
    public int deleteDeptOrderAmountStatisticsById(Long id)
    {
        return deptOrderAmountStatisticsMapper.deleteDeptOrderAmountStatisticsById(id);
    }

    /**
     * 批量插入部门订单交易额统计
     * 
     * @param list 部门订单交易额统计列表
     * @return 结果
     */
    @Override
    public int batchInsertDeptOrderAmountStatistics(List<DeptOrderAmountStatistics> list)
    {
        return deptOrderAmountStatisticsMapper.batchInsertDeptOrderAmountStatistics(list);
    }

    /**
     * 根据统计类型和统计日期删除数据
     * 
     * @param statType 统计类型
     * @param statDate 统计日期
     * @return 结果
     */
    @Override
    public int deleteByStatTypeAndDate(String statType, Date statDate)
    {
        return deptOrderAmountStatisticsMapper.deleteByStatTypeAndDate(statType, statDate);
    }
}
