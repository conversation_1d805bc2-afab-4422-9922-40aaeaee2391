package com.ruoyi.system.service.impl;

import com.ruoyi.system.domain.TotalOpenAccountStatistics;
import com.ruoyi.system.mapper.TotalOpenAccountStatisticsMapper;
import com.ruoyi.system.service.ITotalOpenAccountStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 总开户数据统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-04
 */
@Service
public class TotalOpenAccountStatisticsServiceImpl implements ITotalOpenAccountStatisticsService 
{
    @Autowired
    private TotalOpenAccountStatisticsMapper totalOpenAccountStatisticsMapper;

    /**
     * 查询总开户数据统计
     * 
     * @param id 总开户数据统计主键
     * @return 总开户数据统计
     */
    @Override
    public TotalOpenAccountStatistics selectTotalOpenAccountStatisticsById(Long id)
    {
        return totalOpenAccountStatisticsMapper.selectTotalOpenAccountStatisticsById(id);
    }

    /**
     * 查询总开户数据统计列表
     * 
     * @param totalOpenAccountStatistics 总开户数据统计
     * @return 总开户数据统计
     */
    @Override
    public List<TotalOpenAccountStatistics> selectTotalOpenAccountStatisticsList(TotalOpenAccountStatistics totalOpenAccountStatistics)
    {
        return totalOpenAccountStatisticsMapper.selectTotalOpenAccountStatisticsList(totalOpenAccountStatistics);
    }

    /**
     * 新增总开户数据统计
     * 
     * @param totalOpenAccountStatistics 总开户数据统计
     * @return 结果
     */
    @Override
    public int insertTotalOpenAccountStatistics(TotalOpenAccountStatistics totalOpenAccountStatistics)
    {
        return totalOpenAccountStatisticsMapper.insertTotalOpenAccountStatistics(totalOpenAccountStatistics);
    }

    /**
     * 修改总开户数据统计
     * 
     * @param totalOpenAccountStatistics 总开户数据统计
     * @return 结果
     */
    @Override
    public int updateTotalOpenAccountStatistics(TotalOpenAccountStatistics totalOpenAccountStatistics)
    {
        return totalOpenAccountStatisticsMapper.updateTotalOpenAccountStatistics(totalOpenAccountStatistics);
    }

    /**
     * 批量删除总开户数据统计
     * 
     * @param ids 需要删除的总开户数据统计主键
     * @return 结果
     */
    @Override
    public int deleteTotalOpenAccountStatisticsByIds(Long[] ids)
    {
        return totalOpenAccountStatisticsMapper.deleteTotalOpenAccountStatisticsByIds(ids);
    }

    /**
     * 删除总开户数据统计信息
     * 
     * @param id 总开户数据统计主键
     * @return 结果
     */
    @Override
    public int deleteTotalOpenAccountStatisticsById(Long id)
    {
        return totalOpenAccountStatisticsMapper.deleteTotalOpenAccountStatisticsById(id);
    }

    /**
     * 批量插入总开户数据统计
     * 
     * @param list 总开户数据统计列表
     * @return 结果
     */
    @Override
    public int batchInsertTotalOpenAccountStatistics(List<TotalOpenAccountStatistics> list)
    {
        return totalOpenAccountStatisticsMapper.batchInsertTotalOpenAccountStatistics(list);
    }

    /**
     * 根据统计类型和统计日期查询记录
     * 
     * @param statType 统计类型
     * @param statDate 统计日期
     * @return 总开户数据统计
     */
    @Override
    public TotalOpenAccountStatistics selectByStatTypeAndDate(String statType, Date statDate)
    {
        return totalOpenAccountStatisticsMapper.selectByStatTypeAndDate(statType, statDate);
    }

    /**
     * 根据统计类型和统计日期更新或插入记录
     * 
     * @param totalOpenAccountStatistics 总开户数据统计
     * @return 结果
     */
    @Override
    public int insertOrUpdateTotalOpenAccountStatistics(TotalOpenAccountStatistics totalOpenAccountStatistics)
    {
        return totalOpenAccountStatisticsMapper.insertOrUpdateTotalOpenAccountStatistics(totalOpenAccountStatistics);
    }
}
