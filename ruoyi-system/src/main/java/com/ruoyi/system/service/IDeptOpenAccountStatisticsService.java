package com.ruoyi.system.service;

import com.ruoyi.system.domain.DeptOpenAccountStatistics;

import java.util.Date;
import java.util.List;

/**
 * 部门开户数据统计Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-04
 */
public interface IDeptOpenAccountStatisticsService 
{
    /**
     * 查询部门开户数据统计
     * 
     * @param id 部门开户数据统计主键
     * @return 部门开户数据统计
     */
    public DeptOpenAccountStatistics selectDeptOpenAccountStatisticsById(Long id);

    /**
     * 查询部门开户数据统计列表
     * 
     * @param deptOpenAccountStatistics 部门开户数据统计
     * @return 部门开户数据统计集合
     */
    public List<DeptOpenAccountStatistics> selectDeptOpenAccountStatisticsList(DeptOpenAccountStatistics deptOpenAccountStatistics);

    /**
     * 新增部门开户数据统计
     * 
     * @param deptOpenAccountStatistics 部门开户数据统计
     * @return 结果
     */
    public int insertDeptOpenAccountStatistics(DeptOpenAccountStatistics deptOpenAccountStatistics);

    /**
     * 修改部门开户数据统计
     * 
     * @param deptOpenAccountStatistics 部门开户数据统计
     * @return 结果
     */
    public int updateDeptOpenAccountStatistics(DeptOpenAccountStatistics deptOpenAccountStatistics);

    /**
     * 批量删除部门开户数据统计
     * 
     * @param ids 需要删除的部门开户数据统计主键集合
     * @return 结果
     */
    public int deleteDeptOpenAccountStatisticsByIds(Long[] ids);

    /**
     * 删除部门开户数据统计信息
     * 
     * @param id 部门开户数据统计主键
     * @return 结果
     */
    public int deleteDeptOpenAccountStatisticsById(Long id);

    /**
     * 批量插入部门开户数据统计
     * 
     * @param list 部门开户数据统计列表
     * @return 结果
     */
    public int batchInsertDeptOpenAccountStatistics(List<DeptOpenAccountStatistics> list);

    /**
     * 根据部门名称、统计类型和统计日期查询记录
     * 
     * @param deptName 部门名称
     * @param statType 统计类型
     * @param statDate 统计日期
     * @return 部门开户数据统计
     */
    public DeptOpenAccountStatistics selectByDeptNameAndStatTypeAndDate(String deptName, String statType, Date statDate);

    /**
     * 根据部门名称、统计类型和统计日期更新或插入记录
     * 
     * @param deptOpenAccountStatistics 部门开户数据统计
     * @return 结果
     */
    public int insertOrUpdateDeptOpenAccountStatistics(DeptOpenAccountStatistics deptOpenAccountStatistics);
}
