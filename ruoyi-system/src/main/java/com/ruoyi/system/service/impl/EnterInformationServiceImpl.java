package com.ruoyi.system.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Calendar;
import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.TeacherEnter;
import com.ruoyi.system.domain.TeacherResourcePackage;
import com.ruoyi.system.domain.dto.DyMiniClassDTO;
import com.ruoyi.system.domain.dto.EnterInformationStatDTO;
import com.ruoyi.system.domain.vo.EnterInformationVO;
import com.ruoyi.system.domain.vo.TeacherEnterStatusVO;
import com.ruoyi.system.domain.vo.TeacherEnterVO;
import com.ruoyi.system.dto.ChangeShopMobileDTO;
import com.ruoyi.system.dto.PasswordInfoDTO;
import com.ruoyi.system.dto.TeacherDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.EnterInformationMapper;
import com.ruoyi.system.domain.EnterInformation;
import com.ruoyi.system.service.IEnterInformationService;

/**
 *  入驻信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-21
 */
@Service
public class EnterInformationServiceImpl implements IEnterInformationService
{
    @Autowired
    private EnterInformationMapper enterInformationMapper;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询 入驻信息
     * 
     * @param id  入驻信息主键
     * @return  入驻信息
     */
    @Override
    public EnterInformation selectEnterInformationById(Long id)
    {
        return enterInformationMapper.selectEnterInformationById(id);
    }

    /**
     * 查询 入驻信息列表
     * 
     * @param enterInformation  入驻信息
     * @return  入驻信息
     */
    @Override
    public List<EnterInformation> selectEnterInformationList(EnterInformation enterInformation)
    {
        return enterInformationMapper.selectEnterInformationList(enterInformation);
    }

    /**
     * 新增 入驻信息
     * 
     * @param enterInformation  入驻信息
     * @return 结果
     */
    @Override
    public int insertEnterInformation(EnterInformation enterInformation)
    {
        enterInformation.setCreateTime(DateUtils.getNowDate());
        return enterInformationMapper.insertEnterInformation(enterInformation);
    }

    /**
     * 修改 入驻信息
     * 
     * @param enterInformation  入驻信息
     * @return 结果
     */
    @Override
    public int updateEnterInformation(EnterInformation enterInformation)
    {
        enterInformation.setUpdateTime(DateUtils.getNowDate());
        return enterInformationMapper.updateEnterInformation(enterInformation);
    }

    /**
     * 批量删除 入驻信息
     * 
     * @param ids 需要删除的 入驻信息主键
     * @return 结果
     */
    @Override
    public int deleteEnterInformationByIds(Long[] ids)
    {
        return enterInformationMapper.deleteEnterInformationByIds(ids);
    }

    /**
     * 删除 入驻信息信息
     * 
     * @param id  入驻信息主键
     * @return 结果
     */
    @Override
    public int deleteEnterInformationById(Long id)
    {
        return enterInformationMapper.deleteEnterInformationById(id);
    }

    @Override
    public EnterInformation selectEnterInformationByShopId(Long teacherId) {
        return enterInformationMapper.selectEnterInformationByShopId(teacherId);
    }

    @Override
    public PageInfo<EnterInformation> selectAll(EnterInformationVO enterInformationVO) {
        PageHelper.startPage(enterInformationVO.getPageNum(),enterInformationVO.getPageSize());
        List<EnterInformation> enterInformationList = enterInformationMapper.selectAll(enterInformationVO);
        return new PageInfo<>(enterInformationList);
    }

    @Override
    public List<EnterInformation> getEnterInformationList(EnterInformation enterInformation1) {
        return enterInformationMapper.getEnterInformationList(enterInformation1);
    }

    @Override
    public PageInfo<TeacherEnter> getTeacherEnterList(TeacherEnterVO teacherEnterVO) {
        PageHelper.startPage(teacherEnterVO.getPageNum(),teacherEnterVO.getPageSize());
        List<TeacherEnter> teacherEnterList = enterInformationMapper.getTeacherEnterList(teacherEnterVO);
        return new PageInfo<>(teacherEnterList);
    }

    @Override
    public List<DyMiniClassDTO> selectDyMiniClassList() {
        return enterInformationMapper.selectDyMiniClassList();
    }

    /**
     * 修改入驻申请状态
     */
    @Override
    public boolean updateTeacherEnterStatus(TeacherEnterStatusVO enterStatusVO) {
        enterStatusVO.setUpdateTime(DateUtils.getNowDate());
        enterStatusVO.setStatus(1);
        return enterInformationMapper.updateTeacherEnterStatus(enterStatusVO);
    }

    @Override
    public void updateTeacherPlatfrom(String platform,Integer version,Long teacherId,Integer giftCourse,Integer wapLiveOpen,String ddShopIds, Date endDate,Integer openPromoter,Integer promoterNum) {
        enterInformationMapper.updateTeacherPlatfrom(platform,version,teacherId,giftCourse,wapLiveOpen,ddShopIds,endDate,openPromoter,promoterNum);
    }

    @Override
    public PasswordInfoDTO selectTeacherInfoByAppNameTypeAndTelNum(EnterInformation enterInformation) {
        return enterInformationMapper.selectTeacherInfoByAppNameTypeAndTelNum(enterInformation);
    }

    @Override
    public int selectCountTeacherInfo(String telNum, Integer appNameType) {
        return enterInformationMapper.selectCountTeacherInfo(telNum,appNameType);
    }

    @Override
    public int insertTTeacher(TeacherDTO tTeacher) {
        return enterInformationMapper.insertTTeacher(tTeacher);
    }

    @Override
    public int updateTeacherMobile(ChangeShopMobileDTO changeShopMobileDTO) {
        return enterInformationMapper.updateTeacherMobile(changeShopMobileDTO);
    }

    @Override
    public int updateSysUserByNewMobile(ChangeShopMobileDTO changeShopMobileDTO) {
        return enterInformationMapper.updateSysUserByNewMobile(changeShopMobileDTO);
    }

    @Override
    public int updateEnterInformationTelNum(ChangeShopMobileDTO changeShopMobileDTO) {
        return enterInformationMapper.updateEnterInformationTelNum(changeShopMobileDTO);
    }

    @Override
    public int insertSmsRecord(String phoneNumber, String code, String uuid) {
        return enterInformationMapper.insertSmsRecord(phoneNumber,code,uuid);
    }

    @Override
    public int countSmsRecord(ChangeShopMobileDTO changeShopMobileDTO) {
        return enterInformationMapper.countSmsRecord(changeShopMobileDTO);
    }

    @Override
    public Long selectSopIdForCheckSubAccount(String telNum, Integer appNameType) {
        return enterInformationMapper.selectSopIdForCheckSubAccount(telNum,appNameType);
    }

    @Override
    public void updateTeacherFroKnowledgeStore(TeacherDTO tTeacher) {
        enterInformationMapper.updateTeacherFroKnowledgeStore(tTeacher);
    }

    @Override
    public TeacherDTO selectTeacherKnownledgeStoreDomain(Long teacherId) {
        return enterInformationMapper.selectTeacherKnownledgeStoreDomain(teacherId);
    }

    @Override
    public void insertTeacherResourcePkg(TeacherResourcePackage teacherResourcePackage) {
        enterInformationMapper.insertTeacherResourcePkg(teacherResourcePackage);
    }

    @Override
    public List<EnterInformationStatDTO> queryByTimeAndSpecialist(Date startTime, Date endTime, String accountSpecialist, Integer auditType) {
        return enterInformationMapper.queryByTimeAndSpecialist(startTime, endTime, accountSpecialist, auditType);
    }

    @Override
    public List<Map<String, Object>> querySevenDaysOpenCount() {
        // 生成缓存key，基于当前日期，确保每天的缓存是独立的
        String cacheKey = "seven_days_open_count:" + DateUtils.parseDateToStr("yyyy-MM-dd", new Date());

        // 先从缓存中获取数据
        List<Map<String, Object>> cachedResult = redisCache.getCacheObject(cacheKey);
        if (cachedResult != null && !cachedResult.isEmpty()) {
            return cachedResult;
        }

        List<Map<String, Object>> result = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();

        // 从昨天开始往前推7天
        calendar.add(Calendar.DAY_OF_MONTH, -1);

        for (int i = 0; i < 7; i++) {
            // 设置当天的开始时间（0点）
            Calendar startCalendar = (Calendar) calendar.clone();
            startCalendar.set(Calendar.HOUR_OF_DAY, 0);
            startCalendar.set(Calendar.MINUTE, 0);
            startCalendar.set(Calendar.SECOND, 0);
            startCalendar.set(Calendar.MILLISECOND, 0);
            Date startTime = startCalendar.getTime();

            // 设置当天的结束时间（下一天的0点，不包含）
            Calendar endCalendar = (Calendar) startCalendar.clone();
            endCalendar.add(Calendar.DAY_OF_MONTH, 1);
            Date endTime = endCalendar.getTime();

            // 查询当天audit_type为0的开户数量
            int count = enterInformationMapper.countByTimeAndAuditType(startTime, endTime, 0);

            // 构建返回数据
            Map<String, Object> dayResult = new HashMap<>();
            dayResult.put("date", DateUtils.parseDateToStr("yyyy-MM-dd", startTime));
            dayResult.put("count", count);
            result.add(dayResult);

            // 往前推一天
            calendar.add(Calendar.DAY_OF_MONTH, -1);
        }

        // 将结果缓存30分钟
        redisCache.setCacheObject(cacheKey, result, 24, TimeUnit.HOURS);

        return result;
    }

    @Override
    public List<Map<String, Object>> querySevenDaysOrderAmount() {
        // 生成缓存key，基于当前日期，确保每天的缓存是独立的
        String cacheKey = "seven_days_order_amount:" + DateUtils.parseDateToStr("yyyy-MM-dd", new Date());

        // 先从缓存中获取数据
        List<Map<String, Object>> cachedResult = redisCache.getCacheObject(cacheKey);
        if (cachedResult != null && !cachedResult.isEmpty()) {
            return cachedResult;
        }

        List<Map<String, Object>> result = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();

        // 从昨天开始往前推7天
        calendar.add(Calendar.DAY_OF_MONTH, -1);

        for (int i = 0; i < 7; i++) {
            // 设置当天的开始时间（0点）
            Calendar startCalendar = (Calendar) calendar.clone();
            startCalendar.set(Calendar.HOUR_OF_DAY, 0);
            startCalendar.set(Calendar.MINUTE, 0);
            startCalendar.set(Calendar.SECOND, 0);
            startCalendar.set(Calendar.MILLISECOND, 0);
            Date startTime = startCalendar.getTime();

            // 设置当天的结束时间（下一天的0点，不包含）
            Calendar endCalendar = (Calendar) startCalendar.clone();
            endCalendar.add(Calendar.DAY_OF_MONTH, 1);
            Date endTime = endCalendar.getTime();

            // 查询当天order_status为1的订单交易总额
            BigDecimal totalAmount = enterInformationMapper.sumOrderAmountByTimeAndStatus(startTime, endTime, 1);

            // 构建返回数据
            Map<String, Object> dayResult = new HashMap<>();
            dayResult.put("date", DateUtils.parseDateToStr("yyyy-MM-dd", startTime));
            dayResult.put("amount", totalAmount != null ? totalAmount : BigDecimal.ZERO);
            result.add(dayResult);

            // 往前推一天
            calendar.add(Calendar.DAY_OF_MONTH, -1);
        }

        // 将结果缓存30分钟
        redisCache.setCacheObject(cacheKey, result, 24, TimeUnit.HOURS);

        return result;
    }

    @Override
    public BigDecimal queryOrderAmountByTimeRange(Date startTime, Date endTime) {
        return enterInformationMapper.sumOrderAmountByTimeAndStatus(startTime, endTime, 1);
    }

    @Override
    public int countByTimeAndAuditType(Date startTime, Date endTime, Integer auditType) {
        return enterInformationMapper.countByTimeAndAuditType(startTime, endTime, auditType);
    }

    @Override
    public int countByTimeAndSpecialist(Date startTime, Date endTime, String accountSpecialist, Integer auditType) {
        return enterInformationMapper.countByTimeAndSpecialist(startTime, endTime, accountSpecialist, auditType);
    }

    @Override
    public List<Map<String, Object>> queryOpenAccountRankingByNickNames(Date startTime, Date endTime, List<String> nickNames, Integer auditType, Integer limit) {
        return enterInformationMapper.queryOpenAccountRankingByNickNames(startTime, endTime, nickNames, auditType, limit);
    }
}
