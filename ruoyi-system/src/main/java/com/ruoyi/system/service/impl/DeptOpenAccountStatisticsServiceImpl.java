package com.ruoyi.system.service.impl;

import com.ruoyi.system.domain.DeptOpenAccountStatistics;
import com.ruoyi.system.mapper.DeptOpenAccountStatisticsMapper;
import com.ruoyi.system.service.IDeptOpenAccountStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 部门开户数据统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-04
 */
@Service
public class DeptOpenAccountStatisticsServiceImpl implements IDeptOpenAccountStatisticsService 
{
    @Autowired
    private DeptOpenAccountStatisticsMapper deptOpenAccountStatisticsMapper;

    /**
     * 查询部门开户数据统计
     * 
     * @param id 部门开户数据统计主键
     * @return 部门开户数据统计
     */
    @Override
    public DeptOpenAccountStatistics selectDeptOpenAccountStatisticsById(Long id)
    {
        return deptOpenAccountStatisticsMapper.selectDeptOpenAccountStatisticsById(id);
    }

    /**
     * 查询部门开户数据统计列表
     * 
     * @param deptOpenAccountStatistics 部门开户数据统计
     * @return 部门开户数据统计
     */
    @Override
    public List<DeptOpenAccountStatistics> selectDeptOpenAccountStatisticsList(DeptOpenAccountStatistics deptOpenAccountStatistics)
    {
        return deptOpenAccountStatisticsMapper.selectDeptOpenAccountStatisticsList(deptOpenAccountStatistics);
    }

    /**
     * 新增部门开户数据统计
     * 
     * @param deptOpenAccountStatistics 部门开户数据统计
     * @return 结果
     */
    @Override
    public int insertDeptOpenAccountStatistics(DeptOpenAccountStatistics deptOpenAccountStatistics)
    {
        return deptOpenAccountStatisticsMapper.insertDeptOpenAccountStatistics(deptOpenAccountStatistics);
    }

    /**
     * 修改部门开户数据统计
     * 
     * @param deptOpenAccountStatistics 部门开户数据统计
     * @return 结果
     */
    @Override
    public int updateDeptOpenAccountStatistics(DeptOpenAccountStatistics deptOpenAccountStatistics)
    {
        return deptOpenAccountStatisticsMapper.updateDeptOpenAccountStatistics(deptOpenAccountStatistics);
    }

    /**
     * 批量删除部门开户数据统计
     * 
     * @param ids 需要删除的部门开户数据统计主键
     * @return 结果
     */
    @Override
    public int deleteDeptOpenAccountStatisticsByIds(Long[] ids)
    {
        return deptOpenAccountStatisticsMapper.deleteDeptOpenAccountStatisticsByIds(ids);
    }

    /**
     * 删除部门开户数据统计信息
     * 
     * @param id 部门开户数据统计主键
     * @return 结果
     */
    @Override
    public int deleteDeptOpenAccountStatisticsById(Long id)
    {
        return deptOpenAccountStatisticsMapper.deleteDeptOpenAccountStatisticsById(id);
    }

    /**
     * 批量插入部门开户数据统计
     * 
     * @param list 部门开户数据统计列表
     * @return 结果
     */
    @Override
    public int batchInsertDeptOpenAccountStatistics(List<DeptOpenAccountStatistics> list)
    {
        return deptOpenAccountStatisticsMapper.batchInsertDeptOpenAccountStatistics(list);
    }

    /**
     * 根据部门名称、统计类型和统计日期查询记录
     * 
     * @param deptName 部门名称
     * @param statType 统计类型
     * @param statDate 统计日期
     * @return 部门开户数据统计
     */
    @Override
    public DeptOpenAccountStatistics selectByDeptNameAndStatTypeAndDate(String deptName, String statType, Date statDate)
    {
        return deptOpenAccountStatisticsMapper.selectByDeptNameAndStatTypeAndDate(deptName, statType, statDate);
    }

    /**
     * 根据部门名称、统计类型和统计日期更新或插入记录
     * 
     * @param deptOpenAccountStatistics 部门开户数据统计
     * @return 结果
     */
    @Override
    public int insertOrUpdateDeptOpenAccountStatistics(DeptOpenAccountStatistics deptOpenAccountStatistics)
    {
        return deptOpenAccountStatisticsMapper.insertOrUpdateDeptOpenAccountStatistics(deptOpenAccountStatistics);
    }
}
