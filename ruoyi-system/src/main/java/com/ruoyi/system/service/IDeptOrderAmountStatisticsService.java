package com.ruoyi.system.service;

import java.util.Date;
import java.util.List;
import com.ruoyi.system.domain.DeptOrderAmountStatistics;

/**
 * 部门订单交易额统计Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public interface IDeptOrderAmountStatisticsService 
{
    /**
     * 查询部门订单交易额统计
     * 
     * @param id 部门订单交易额统计主键
     * @return 部门订单交易额统计
     */
    public DeptOrderAmountStatistics selectDeptOrderAmountStatisticsById(Long id);

    /**
     * 查询部门订单交易额统计列表
     * 
     * @param deptOrderAmountStatistics 部门订单交易额统计
     * @return 部门订单交易额统计集合
     */
    public List<DeptOrderAmountStatistics> selectDeptOrderAmountStatisticsList(DeptOrderAmountStatistics deptOrderAmountStatistics);

    /**
     * 新增部门订单交易额统计
     * 
     * @param deptOrderAmountStatistics 部门订单交易额统计
     * @return 结果
     */
    public int insertDeptOrderAmountStatistics(DeptOrderAmountStatistics deptOrderAmountStatistics);

    /**
     * 修改部门订单交易额统计
     * 
     * @param deptOrderAmountStatistics 部门订单交易额统计
     * @return 结果
     */
    public int updateDeptOrderAmountStatistics(DeptOrderAmountStatistics deptOrderAmountStatistics);

    /**
     * 批量删除部门订单交易额统计
     * 
     * @param ids 需要删除的部门订单交易额统计主键集合
     * @return 结果
     */
    public int deleteDeptOrderAmountStatisticsByIds(Long[] ids);

    /**
     * 删除部门订单交易额统计信息
     * 
     * @param id 部门订单交易额统计主键
     * @return 结果
     */
    public int deleteDeptOrderAmountStatisticsById(Long id);

    /**
     * 批量插入部门订单交易额统计
     * 
     * @param list 部门订单交易额统计列表
     * @return 结果
     */
    public int batchInsertDeptOrderAmountStatistics(List<DeptOrderAmountStatistics> list);

    /**
     * 根据统计类型和统计日期删除数据
     * 
     * @param statType 统计类型
     * @param statDate 统计日期
     * @return 结果
     */
    public int deleteByStatTypeAndDate(String statType, Date statDate);
}
