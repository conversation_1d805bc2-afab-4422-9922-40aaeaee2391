package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.TotalOpenAccountStatistics;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 总开户数据统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-04
 */
public interface TotalOpenAccountStatisticsMapper 
{
    /**
     * 查询总开户数据统计
     * 
     * @param id 总开户数据统计主键
     * @return 总开户数据统计
     */
    public TotalOpenAccountStatistics selectTotalOpenAccountStatisticsById(Long id);

    /**
     * 查询总开户数据统计列表
     * 
     * @param totalOpenAccountStatistics 总开户数据统计
     * @return 总开户数据统计集合
     */
    public List<TotalOpenAccountStatistics> selectTotalOpenAccountStatisticsList(TotalOpenAccountStatistics totalOpenAccountStatistics);

    /**
     * 新增总开户数据统计
     * 
     * @param totalOpenAccountStatistics 总开户数据统计
     * @return 结果
     */
    public int insertTotalOpenAccountStatistics(TotalOpenAccountStatistics totalOpenAccountStatistics);

    /**
     * 修改总开户数据统计
     * 
     * @param totalOpenAccountStatistics 总开户数据统计
     * @return 结果
     */
    public int updateTotalOpenAccountStatistics(TotalOpenAccountStatistics totalOpenAccountStatistics);

    /**
     * 删除总开户数据统计
     * 
     * @param id 总开户数据统计主键
     * @return 结果
     */
    public int deleteTotalOpenAccountStatisticsById(Long id);

    /**
     * 批量删除总开户数据统计
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTotalOpenAccountStatisticsByIds(Long[] ids);

    /**
     * 批量插入总开户数据统计
     * 
     * @param list 总开户数据统计列表
     * @return 结果
     */
    public int batchInsertTotalOpenAccountStatistics(List<TotalOpenAccountStatistics> list);

    /**
     * 根据统计类型和统计日期查询记录
     * 
     * @param statType 统计类型
     * @param statDate 统计日期
     * @return 总开户数据统计
     */
    public TotalOpenAccountStatistics selectByStatTypeAndDate(@Param("statType") String statType, 
                                                             @Param("statDate") Date statDate);

    /**
     * 根据统计类型和统计日期更新或插入记录
     * 
     * @param totalOpenAccountStatistics 总开户数据统计
     * @return 结果
     */
    public int insertOrUpdateTotalOpenAccountStatistics(TotalOpenAccountStatistics totalOpenAccountStatistics);
}
