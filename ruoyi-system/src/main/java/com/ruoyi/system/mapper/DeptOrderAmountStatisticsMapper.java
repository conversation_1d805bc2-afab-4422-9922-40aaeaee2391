package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.DeptOrderAmountStatistics;
import org.apache.ibatis.annotations.Param;

/**
 * 部门订单交易额统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public interface DeptOrderAmountStatisticsMapper 
{
    /**
     * 查询部门订单交易额统计
     * 
     * @param id 部门订单交易额统计主键
     * @return 部门订单交易额统计
     */
    public DeptOrderAmountStatistics selectDeptOrderAmountStatisticsById(Long id);

    /**
     * 查询部门订单交易额统计列表
     * 
     * @param deptOrderAmountStatistics 部门订单交易额统计
     * @return 部门订单交易额统计集合
     */
    public List<DeptOrderAmountStatistics> selectDeptOrderAmountStatisticsList(DeptOrderAmountStatistics deptOrderAmountStatistics);

    /**
     * 新增部门订单交易额统计
     * 
     * @param deptOrderAmountStatistics 部门订单交易额统计
     * @return 结果
     */
    public int insertDeptOrderAmountStatistics(DeptOrderAmountStatistics deptOrderAmountStatistics);

    /**
     * 修改部门订单交易额统计
     * 
     * @param deptOrderAmountStatistics 部门订单交易额统计
     * @return 结果
     */
    public int updateDeptOrderAmountStatistics(DeptOrderAmountStatistics deptOrderAmountStatistics);

    /**
     * 删除部门订单交易额统计
     * 
     * @param id 部门订单交易额统计主键
     * @return 结果
     */
    public int deleteDeptOrderAmountStatisticsById(Long id);

    /**
     * 批量删除部门订单交易额统计
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDeptOrderAmountStatisticsByIds(Long[] ids);

    /**
     * 批量插入部门订单交易额统计
     * 
     * @param list 部门订单交易额统计列表
     * @return 结果
     */
    public int batchInsertDeptOrderAmountStatistics(@Param("list") List<DeptOrderAmountStatistics> list);

    /**
     * 根据统计类型和统计日期删除数据
     * 
     * @param statType 统计类型
     * @param statDate 统计日期
     * @return 结果
     */
    public int deleteByStatTypeAndDate(@Param("statType") String statType, @Param("statDate") java.util.Date statDate);
}
