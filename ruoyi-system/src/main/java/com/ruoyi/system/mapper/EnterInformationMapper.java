package com.ruoyi.system.mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.math.BigDecimal;
import com.ruoyi.system.domain.EnterInformation;
import com.ruoyi.system.domain.TeacherEnter;
import com.ruoyi.system.domain.TeacherResourcePackage;
import com.ruoyi.system.domain.dto.DyMiniClassDTO;
import com.ruoyi.system.domain.dto.EnterInformationStatDTO;
import com.ruoyi.system.domain.vo.EnterInformationVO;
import com.ruoyi.system.domain.vo.TeacherEnterStatusVO;
import com.ruoyi.system.domain.vo.TeacherEnterVO;
import com.ruoyi.system.dto.ChangeShopMobileDTO;
import com.ruoyi.system.dto.PasswordInfoDTO;
import com.ruoyi.system.dto.TeacherDTO;
import org.apache.ibatis.annotations.Param;

/**
 *  入驻信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-21
 */
public interface EnterInformationMapper 
{
    /**
     * 查询 入驻信息
     * 
     * @param id  入驻信息主键
     * @return  入驻信息
     */
    public EnterInformation selectEnterInformationById(Long id);

    /**
     * 查询 入驻信息列表
     * 
     * @param enterInformation  入驻信息
     * @return  入驻信息集合
     */
    public List<EnterInformation> selectEnterInformationList(EnterInformation enterInformation);

    /**
     * 新增 入驻信息
     * 
     * @param enterInformation  入驻信息
     * @return 结果
     */
    public int insertEnterInformation(EnterInformation enterInformation);

    /**
     * 修改 入驻信息
     * 
     * @param enterInformation  入驻信息
     * @return 结果
     */
    public int updateEnterInformation(EnterInformation enterInformation);

    /**
     * 删除 入驻信息
     * 
     * @param id  入驻信息主键
     * @return 结果
     */
    public int deleteEnterInformationById(Long id);

    /**
     * 批量删除 入驻信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEnterInformationByIds(Long[] ids);

    EnterInformation selectEnterInformationByShopId(@Param("shopId") Long shopId);

    List<EnterInformation> selectAll(EnterInformationVO enterInformationVO);

    List<EnterInformation> getEnterInformationList(EnterInformation enterInformation);

    List<TeacherEnter> getTeacherEnterList(TeacherEnterVO teacherEnterVO);

    List<DyMiniClassDTO> selectDyMiniClassList();

    boolean updateTeacherEnterStatus(TeacherEnterStatusVO enterStatusVO);

    void updateTeacherPlatfrom(@Param("platform") String platform
            ,@Param("version")Integer version
            ,@Param("teacherId")Long teacherId
            ,@Param("giftCourse")Integer giftCourse
            ,@Param("wapLiveOpen")Integer wapLiveOpen
            ,@Param("ddShopIds")String ddShopIds,@Param("endDate") Date endDate,@Param("openPromoter")Integer openPromoter,@Param("promoterNum")Integer promoterNum);

    PasswordInfoDTO selectTeacherInfoByAppNameTypeAndTelNum(EnterInformation enterInformation);

    int selectCountTeacherInfo(@Param("telNum")String telNum, @Param("appNameType")Integer appNameType);

    int insertTTeacher(TeacherDTO tTeacher);

    int updateTeacherMobile(ChangeShopMobileDTO changeShopMobileDTO);

    int updateSysUserByNewMobile(ChangeShopMobileDTO changeShopMobileDTO);

    int updateEnterInformationTelNum(ChangeShopMobileDTO changeShopMobileDTO);

    int insertSmsRecord(@Param("phoneNumber") String phoneNumber,@Param("code") String code,@Param("codeIndexKey") String uuid);

    int countSmsRecord(ChangeShopMobileDTO changeShopMobileDTO);

    Long selectSopIdForCheckSubAccount(@Param("telNum") String telNum, @Param("appNameType")Integer appNameType);

    void updateTeacherFroKnowledgeStore(TeacherDTO tTeacher);

    TeacherDTO selectTeacherKnownledgeStoreDomain(@Param("teacherId")Long teacherId);

    void insertTeacherResourcePkg(TeacherResourcePackage teacherResourcePackage);

    /**
     * 根据时间范围、客户专员和审核类型查询入驻信息
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param accountSpecialist 客户专员
     * @param auditType 审核类型
     * @return 入驻信息列表
     */
    List<EnterInformationStatDTO> queryByTimeAndSpecialist(@Param("startTime") Date startTime,
                                                           @Param("endTime") Date endTime,
                                                           @Param("accountSpecialist") String accountSpecialist,
                                                           @Param("auditType") Integer auditType);

    /**
     * 根据时间范围和审核类型统计开户数量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param auditType 审核类型
     * @return 开户数量
     */
    int countByTimeAndAuditType(@Param("startTime") Date startTime,
                               @Param("endTime") Date endTime,
                               @Param("auditType") Integer auditType);

    /**
     * 根据时间范围和订单状态统计订单交易总额
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param orderStatus 订单状态
     * @return 订单交易总额
     */
    BigDecimal sumOrderAmountByTimeAndStatus(@Param("startTime") Date startTime,
                                           @Param("endTime") Date endTime,
                                           @Param("orderStatus") Integer orderStatus);

    /**
     * 根据时间范围、客户专员和审核类型统计开户数量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param accountSpecialist 客户专员
     * @param auditType 审核类型
     * @return 开户数量
     */
    int countByTimeAndSpecialist(@Param("startTime") Date startTime,
                               @Param("endTime") Date endTime,
                               @Param("accountSpecialist") String accountSpecialist,
                               @Param("auditType") Integer auditType);
}
