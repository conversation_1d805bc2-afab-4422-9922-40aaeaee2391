package com.ruoyi.system.mapper;

import java.math.BigDecimal;
import java.util.List;
import com.ruoyi.system.domain.EmployeeSalesStatistics;
import org.apache.ibatis.annotations.Param;

/**
 * 员工销售数据统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface EmployeeSalesStatisticsMapper 
{
    /**
     * 查询员工销售数据统计
     * 
     * @param id 员工销售数据统计主键
     * @return 员工销售数据统计
     */
    public EmployeeSalesStatistics selectEmployeeSalesStatisticsById(Long id);

    /**
     * 查询员工销售数据统计列表
     * 
     * @param employeeSalesStatistics 员工销售数据统计
     * @return 员工销售数据统计集合
     */
    public List<EmployeeSalesStatistics> selectEmployeeSalesStatisticsList(EmployeeSalesStatistics employeeSalesStatistics);

    /**
     * 新增员工销售数据统计
     * 
     * @param employeeSalesStatistics 员工销售数据统计
     * @return 结果
     */
    public int insertEmployeeSalesStatistics(EmployeeSalesStatistics employeeSalesStatistics);

    /**
     * 修改员工销售数据统计
     * 
     * @param employeeSalesStatistics 员工销售数据统计
     * @return 结果
     */
    public int updateEmployeeSalesStatistics(EmployeeSalesStatistics employeeSalesStatistics);

    /**
     * 删除员工销售数据统计
     * 
     * @param id 员工销售数据统计主键
     * @return 结果
     */
    public int deleteEmployeeSalesStatisticsById(Long id);

    /**
     * 批量删除员工销售数据统计
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmployeeSalesStatisticsByIds(Long[] ids);

    EmployeeSalesStatistics selectEmployeeSalesStatisticsByUserId(@Param("userId") Long userId,@Param("statType") String statType);

    int queryHasTeam(@Param("userId")Long userId);

    EmployeeSalesStatistics selectEmployeeSalesStatisticsByParentLeaderId(@Param("userId")Long userId, @Param("statType")String statType);

    EmployeeSalesStatistics selectEmployeeSalesStatisticsByParentLeaderIdAndDeptId(@Param("userId")Long userId, @Param("statType")String statType);

    EmployeeSalesStatistics selectEmployeeSalesStatisticsByLeaderId(@Param("userId")Long userId, @Param("statType")String statType);

    EmployeeSalesStatistics selectEmployeeSalesStatisticsByLeaderIdAndDeptId(@Param("userId")Long userId, @Param("statType")String statType);

    /**
     * 根据用户ID列表和统计类型查询销售总额
     * @param userIds 用户ID列表
     * @param statType 统计类型
     * @return 销售总额
     */
    BigDecimal sumTotalSalesByUserIdsAndStatType(@Param("userIds") List<Long> userIds, @Param("statType") String statType);
}