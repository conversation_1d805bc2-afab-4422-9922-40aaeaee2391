-- 测试员工开户数排行查询SQL
-- 这个SQL用于验证我们在EmployeeDataRankingListController中实现的逻辑

-- 1. 查询所有员工在指定时间范围内的开户数排行（示例：本月数据）
SELECT 
    ei.account_specialist as nickName,
    COUNT(1) as openAccountCount
FROM enter_information ei
WHERE ei.audit_type = 0  -- 审核通过
  AND ei.account_specialist IN ('张三', '李四', '王五', '赵六', '孙七', '周八')  -- 示例员工昵称列表
  AND ei.update_time >= '2025-08-01 00:00:00'  -- 本月1号0点
  AND ei.update_time < '2025-08-04 00:00:00'   -- 今天0点（不包含）
GROUP BY ei.account_specialist
ORDER BY COUNT(1) DESC
LIMIT 6;

-- 2. 查询昨天的开户数排行
SELECT 
    ei.account_specialist as nick<PERSON><PERSON>,
    COUNT(1) as openAccountCount
FROM enter_information ei
WHERE ei.audit_type = 0  -- 审核通过
  AND ei.account_specialist IN ('张三', '李四', '王五', '赵六', '孙七', '周八')  -- 示例员工昵称列表
  AND ei.update_time >= '2025-08-03 00:00:00'  -- 昨天0点
  AND ei.update_time < '2025-08-04 00:00:00'   -- 今天0点（不包含）
GROUP BY ei.account_specialist
ORDER BY COUNT(1) DESC
LIMIT 6;

-- 3. 查询7天的开户数排行
SELECT 
    ei.account_specialist as nickName,
    COUNT(1) as openAccountCount
FROM enter_information ei
WHERE ei.audit_type = 0  -- 审核通过
  AND ei.account_specialist IN ('张三', '李四', '王五', '赵六', '孙七', '周八')  -- 示例员工昵称列表
  AND ei.update_time >= '2025-07-28 00:00:00'  -- 7天前0点
  AND ei.update_time < '2025-08-04 00:00:00'   -- 今天0点（不包含）
GROUP BY ei.account_specialist
ORDER BY COUNT(1) DESC
LIMIT 6;

-- 4. 查询本年的开户数排行
SELECT 
    ei.account_specialist as nickName,
    COUNT(1) as openAccountCount
FROM enter_information ei
WHERE ei.audit_type = 0  -- 审核通过
  AND ei.account_specialist IN ('张三', '李四', '王五', '赵六', '孙七', '周八')  -- 示例员工昵称列表
  AND ei.update_time >= '2025-01-01 00:00:00'  -- 本年1月1号0点
  AND ei.update_time < '2025-08-04 00:00:00'   -- 今天0点（不包含）
GROUP BY ei.account_specialist
ORDER BY COUNT(1) DESC
LIMIT 6;

-- 5. 验证数据完整性 - 查看所有商务部门员工的开户情况
SELECT 
    ei.account_specialist as nickName,
    COUNT(1) as openAccountCount,
    MIN(ei.update_time) as firstOpenTime,
    MAX(ei.update_time) as lastOpenTime
FROM enter_information ei
WHERE ei.audit_type = 0  -- 审核通过
  AND ei.account_specialist IS NOT NULL
  AND ei.account_specialist != ''
GROUP BY ei.account_specialist
HAVING COUNT(1) > 0
ORDER BY COUNT(1) DESC;

-- 6. 查看不同审核状态的数据分布
SELECT 
    audit_type,
    COUNT(*) as count,
    CASE 
        WHEN audit_type = 0 THEN '审核通过'
        WHEN audit_type = 1 THEN '审核中'
        WHEN audit_type = 2 THEN '驳回'
        ELSE '未知状态'
    END as audit_type_desc
FROM enter_information 
WHERE account_specialist IS NOT NULL
  AND account_specialist != ''
GROUP BY audit_type
ORDER BY audit_type;

-- 7. 查看最近的开户记录（用于验证时间范围）
SELECT 
    id,
    account_specialist,
    audit_type,
    update_time,
    create_time
FROM enter_information 
WHERE audit_type = 0 
  AND account_specialist IS NOT NULL
  AND account_specialist != ''
ORDER BY update_time DESC 
LIMIT 20;

-- 8. 测试动态参数查询（模拟MyBatis的foreach）
-- 这个查询展示了如何处理员工昵称列表的IN查询
SELECT 
    ei.account_specialist as nickName,
    COUNT(1) as openAccountCount
FROM enter_information ei
WHERE ei.audit_type = 0
  AND ei.account_specialist IN (
    SELECT DISTINCT account_specialist 
    FROM enter_information 
    WHERE account_specialist IS NOT NULL 
      AND account_specialist != ''
    LIMIT 10  -- 模拟获取前10个员工
  )
  AND ei.update_time >= '2025-08-01 00:00:00'
  AND ei.update_time < '2025-08-04 00:00:00'
GROUP BY ei.account_specialist
ORDER BY COUNT(1) DESC
LIMIT 6;
