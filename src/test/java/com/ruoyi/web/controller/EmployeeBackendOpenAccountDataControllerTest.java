package com.ruoyi.web.controller;

import com.ruoyi.web.controller.system.EmployeeBackendOpenAccountDataController;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

/**
 * 开户数据统计功能测试
 */
@SpringBootTest
@SpringJUnitConfig
public class EmployeeBackendOpenAccountDataControllerTest {

    @Autowired
    private EmployeeBackendOpenAccountDataController controller;

    @Test
    public void testQueryOpenAccountAmount() {
        try {
            // 调用开户数据统计方法
            var result = controller.queryOpenAccountAmount();
            
            // 打印结果
            System.out.println("开户数据统计结果：");
            System.out.println(result);
            
            // 验证返回结果不为空
            assert result != null;
            assert result.get("code").equals(200);
            
            System.out.println("测试通过！");
            
        } catch (Exception e) {
            System.err.println("测试失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
}
