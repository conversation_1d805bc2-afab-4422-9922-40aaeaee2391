# 员工开户数排行功能说明

## 功能概述

本功能实现了按时间范围查询所有员工的开户数排行，取前6个，输出员工昵称和开户数。支持昨天、7天、本月、本年四种时间范围。

## 实现文件

### 1. 控制器
- **文件**: `ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/EmployeeDataRankingListController.java`
- **接口**: `GET /employee_backend_data/queryOpenAccountRankList?statType={statType}`
- **功能**: 查询员工开户数排行前6名

### 2. Service层
- **接口**: `ruoyi-system/src/main/java/com/ruoyi/system/service/IEnterInformationService.java`
- **实现**: `ruoyi-system/src/main/java/com/ruoyi/system/service/impl/EnterInformationServiceImpl.java`
- **新增方法**: `queryOpenAccountRankingByNickNames(...)`

### 3. Mapper层
- **接口**: `ruoyi-system/src/main/java/com/ruoyi/system/mapper/EnterInformationMapper.java`
- **XML**: `ruoyi-system/src/main/resources/mapper/system/EnterInformationMapper.xml`
- **新增方法**: `queryOpenAccountRankingByNickNames(...)`

## 核心SQL实现

```sql
SELECT 
    ei.account_specialist as nickName,
    COUNT(1) as openAccountCount
FROM enter_information ei
WHERE ei.audit_type = #{auditType}
    AND ei.account_specialist IN
    <foreach collection="nickNames" item="nickName" open="(" separator="," close=")">
        #{nickName}
    </foreach>
    AND ei.update_time >= #{startTime}
    AND ei.update_time < #{endTime}
GROUP BY ei.account_specialist
ORDER BY COUNT(1) DESC
LIMIT #{limit}
```

## 接口参数

### 请求参数
- **statType** (String): 统计类型，支持以下值：
  - `daily`: 昨天（昨天0点到今天0点）
  - `weekly`: 7天（7天前0点到今天0点）
  - `monthly`: 本月（本月1号0点到今天0点）
  - `yearly`: 本年（本年1月1号0点到今天0点）

### 查询条件
- **audit_type = 0**: 只统计审核通过的记录
- **员工范围**: 商务部（deptId=104）下的所有子部门员工，排除部门名称为"商务部"的用户
- **排序**: 按开户数量降序排列
- **限制**: 返回前6名

## 返回数据结构

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "rankingList": [
      {
        "nickName": "张三",
        "openAccountCount": 25
      },
      {
        "nickName": "李四",
        "openAccountCount": 20
      },
      {
        "nickName": "王五",
        "openAccountCount": 18
      },
      {
        "nickName": "赵六",
        "openAccountCount": 15
      },
      {
        "nickName": "孙七",
        "openAccountCount": 12
      },
      {
        "nickName": "周八",
        "openAccountCount": 10
      }
    ],
    "statType": "monthly",
    "startTime": "2025-08-01 00:00:00",
    "endTime": "2025-08-04 00:00:00",
    "totalCount": 6
  }
}
```

## 数据字段说明

### rankingList（排行列表）
- `nickName`: 员工昵称
- `openAccountCount`: 开户数量

### 其他字段
- `statType`: 统计类型
- `startTime`: 查询开始时间
- `endTime`: 查询结束时间
- `totalCount`: 返回的记录数量

## 使用示例

### 1. 查询本月开户排行
```bash
curl -X GET "http://localhost:8080/employee_backend_data/queryOpenAccountRankList?statType=monthly"
```

### 2. 查询昨天开户排行
```bash
curl -X GET "http://localhost:8080/employee_backend_data/queryOpenAccountRankList?statType=daily"
```

### 3. 查询7天开户排行
```bash
curl -X GET "http://localhost:8080/employee_backend_data/queryOpenAccountRankList?statType=weekly"
```

### 4. 查询本年开户排行
```bash
curl -X GET "http://localhost:8080/employee_backend_data/queryOpenAccountRankList?statType=yearly"
```

## 技术特点

### 1. SQL优化
- 使用GROUP BY和ORDER BY直接在数据库层面完成分组和排序
- 使用LIMIT直接限制返回数量，减少数据传输
- 使用IN查询批量匹配员工昵称，避免多次查询

### 2. 性能优化
- 一次SQL查询完成所有统计和排序
- 避免了在应用层循环查询每个员工的开户数
- 利用数据库索引提高查询效率

### 3. 代码复用
- 复用现有的时间范围处理逻辑
- 复用现有的员工数据获取逻辑
- 新增的服务方法可以被其他功能复用

## 测试验证

### 1. 接口测试
使用上述curl命令测试各种时间范围的查询。

### 2. SQL验证
可以使用 `test_employee_ranking_query.sql` 文件中的SQL语句验证数据的正确性。

### 3. 数据验证
- 验证返回的员工都属于商务部子部门
- 验证开户数量按降序排列
- 验证时间范围的正确性
- 验证只统计audit_type=0的记录

## 注意事项

1. **时间范围**: 所有时间范围都不包含今天的数据
2. **员工范围**: 只统计商务部子部门的员工，排除"商务部"本身
3. **审核状态**: 只统计审核通过（audit_type=0）的记录
4. **排行数量**: 固定返回前6名，如果实际员工数少于6个，返回实际数量
5. **空值处理**: 如果某个时间范围内没有开户记录，对应员工不会出现在排行中

## 扩展功能

1. **可配置排行数量**: 可以将LIMIT参数化，支持动态设置返回数量
2. **部门维度排行**: 可以扩展为按部门统计排行
3. **时间维度扩展**: 可以支持自定义时间范围查询
4. **缓存优化**: 可以为热点查询添加Redis缓存
