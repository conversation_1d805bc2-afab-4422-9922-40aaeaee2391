# 部门订单交易额统计功能说明

## 功能概述

该功能实现了按时间范围查询各部门订单交易额，并将统计结果保存到数据库中。

## 主要功能

1. **按时间范围查询总交易额**：昨天、7天、本月、本年
2. **按部门员工ID列表查询各个部门订单交易额**：从`employee_sales_statistics`表查询
3. **计算其他交易额**：总交易额 - 全部部门交易额之和
4. **保存统计结果到数据库**：将查询结果按stat_type维度保存到`dept_order_amount_statistics`表

## 数据库表结构

### dept_order_amount_statistics 表

```sql
CREATE TABLE `dept_order_amount_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `stat_type` enum('daily','weekly','monthly','yearly') NOT NULL COMMENT '统计类型：daily-昨天,weekly-7天,monthly-本月,yearly-本年',
  `dept_name` varchar(100) NOT NULL COMMENT '部门名称，其他交易额用"其他"表示',
  `total_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '部门交易总额',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_stat` (`stat_type`,`dept_name`,`stat_date`),
  KEY `idx_stat_type` (`stat_type`),
  KEY `idx_dept_name` (`dept_name`),
  KEY `idx_stat_date` (`stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门订单交易额统计表';
```

## API接口

### 查询部门订单交易额

**接口地址**：`POST /employee_backend_data/queryDeptOrderAmount`

**请求参数**：无

**返回数据结构**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalAmount": {
      "daily": "昨天总交易额",
      "weekly": "7天总交易额", 
      "monthly": "本月总交易额",
      "yearly": "本年总交易额"
    },
    "deptAmount": {
      "商务一部": {
        "daily": "昨天交易额",
        "weekly": "7天交易额",
        "monthly": "本月交易额", 
        "yearly": "本年交易额"
      },
      "商务二部": { ... },
      "商务三部": { ... },
      "商务四部": { ... },
      "其他": {
        "daily": "其他昨天交易额",
        "weekly": "其他7天交易额",
        "monthly": "其他本月交易额",
        "yearly": "其他本年交易额"
      }
    }
  }
}
```

## 时间范围说明

- **daily（昨天）**：昨天0点0分0秒 到 今天0点0分0秒（不包含）
- **weekly（7天）**：7天前0点0分0秒 到 今天0点0分0秒（不包含）
- **monthly（本月）**：本月1号0点0分0秒 到 今天0点0分0秒（不包含）
- **yearly（本年）**：本年1月1号0点0分0秒 到 今天0点0分0秒（不包含）

## 数据来源

1. **总交易额**：来自`wendao101-order.course_order`表，条件：`order_status = 1`
2. **部门交易额**：来自`wendao101-order.employee_sales_statistics`表，条件：`dept_id IS NULL`

## 部署步骤

1. **创建数据库表**：
   ```bash
   # 在数据库中执行
   mysql -u username -p database_name < dept_order_amount_statistics.sql
   ```

2. **编译项目**：
   ```bash
   mvn clean compile
   ```

3. **启动应用**：
   ```bash
   mvn spring-boot:run
   ```

## 测试

1. **调用API接口**：
   ```bash
   curl -X POST http://localhost:8080/employee_backend_data/queryDeptOrderAmount
   ```

2. **查看数据库数据**：
   ```sql
   SELECT * FROM dept_order_amount_statistics ORDER BY stat_date DESC, stat_type, dept_name;
   ```

## 注意事项

1. 统计数据会自动保存到数据库，使用了`ON DUPLICATE KEY UPDATE`来处理重复数据
2. 如果保存数据库失败，不会影响API的正常返回，只会在控制台输出错误日志
3. 建议定期清理历史统计数据，避免数据量过大
4. 统计日期使用的是当天的0点时间作为基准

## 文件清单

- `dept_order_amount_statistics.sql` - 数据库建表语句
- `test_dept_order_amount.sql` - 测试SQL语句
- `DeptOrderAmountStatistics.java` - 实体类
- `DeptOrderAmountStatisticsMapper.java` - Mapper接口
- `DeptOrderAmountStatisticsMapper.xml` - Mapper XML
- `IDeptOrderAmountStatisticsService.java` - Service接口
- `DeptOrderAmountStatisticsServiceImpl.java` - Service实现类
- `EmployeeBackendDataController.java` - 控制器（已修改）
