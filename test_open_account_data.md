# 开户数据统计功能实现说明

## 功能概述
实现了按时间范围查询开户数据的统计功能，包括：
1. 按时间范围查询总开户数（昨天、7天、本月、本年）
2. 按部门员工nickName列表和时间参数查询各个部门开户数
3. 计算其他开户数（总开户数 - 全部部门开户数之和）

## 实现的方法

### 1. Controller层
- **文件**: `ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/EmployeeBackendOpenAccountDataController.java`
- **方法**: `queryOpenAccountAmount()`
- **功能**: 统计各部门和其他开户数据

### 2. Service层
- **文件**: `ruoyi-system/src/main/java/com/ruoyi/system/service/IEnterInformationService.java`
- **新增方法**:
  - `countByTimeAndAuditType(Date startTime, Date endTime, Integer auditType)`: 按时间范围和审核类型统计开户数量
  - `countByTimeAndSpecialist(Date startTime, Date endTime, String accountSpecialist, Integer auditType)`: 按时间范围、客户专员和审核类型统计开户数量

### 3. Service实现层
- **文件**: `ruoyi-system/src/main/java/com/ruoyi/system/service/impl/EnterInformationServiceImpl.java`
- **实现了上述两个方法**

### 4. Mapper层
- **文件**: `ruoyi-system/src/main/java/com/ruoyi/system/mapper/EnterInformationMapper.java`
- **新增方法**: `countByTimeAndSpecialist(...)`

### 5. SQL映射
- **文件**: `ruoyi-system/src/main/resources/mapper/system/EnterInformationMapper.xml`
- **新增SQL**: `countByTimeAndSpecialist` 查询

## 时间范围说明
- **daily（昨天）**: 昨天0点0分0秒 到 今天0点0分0秒（不包含）
- **weekly（7天）**: 7天前0点0分0秒 到 今天0点0分0秒（不包含）
- **monthly（本月）**: 本月1号0点0分0秒 到 今天0点0分0秒（不包含）
- **yearly（本年）**: 本年1月1号0点0分0秒 到 今天0点0分0秒（不包含）

## 查询条件
- **audit_type = 0**: 审核通过的记录
- **account_specialist**: 对应nickNameList中的每个nickName
- **update_time**: 在指定时间范围内

## 返回数据结构
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalOpenAccount": {
      "daily": 总昨天开户数,
      "weekly": 总7天开户数,
      "monthly": 总本月开户数,
      "yearly": 总本年开户数
    },
    "deptOpenAccount": {
      "商务一部": {
        "daily": 昨天开户数,
        "weekly": 7天开户数,
        "monthly": 本月开户数,
        "yearly": 本年开户数
      },
      "商务二部": { ... },
      "商务三部": { ... },
      "商务四部": { ... },
      "其他": {
        "daily": 其他昨天开户数,
        "weekly": 其他7天开户数,
        "monthly": 其他本月开户数,
        "yearly": 其他本年开户数
      }
    }
  }
}
```

## 核心SQL示例
```sql
-- 查询某个员工在指定时间范围的开户数量
SELECT COUNT(1) 
FROM enter_information 
WHERE audit_type = 0 
  AND account_specialist = #{nickName} 
  AND update_time >= #{startTime} 
  AND update_time < #{endTime}

-- 查询指定时间范围的总开户数量
SELECT COUNT(1) 
FROM enter_information 
WHERE audit_type = 0 
  AND update_time >= #{startTime} 
  AND update_time < #{endTime}
```

## 计算逻辑
1. 首先查询各个时间范围的总开户数
2. 然后按部门循环，计算每个部门在各时间范围的开户数（部门内所有员工开户数之和）
3. 最后计算"其他"开户数：总开户数 - 所有部门开户数之和

## API调用
- **URL**: `POST /employee_backend_data/queryOpenAccountAmount`
- **参数**: 无需参数，固定查询deptId=104的部门数据
